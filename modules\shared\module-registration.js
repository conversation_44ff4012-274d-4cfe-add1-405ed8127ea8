// Central Module Registration - Single source of truth for all module registrations
// This file imports all modules and registers them with the singleton module registry

import { moduleRegistry } from './module-registry.js';
import { Logger } from './logger.js';

// Import all feature modules
import { PropertyManagement } from '../property-management/js/index.js';
import { BikeManagement } from '../bike-management/js/index.js';
import { EncampmentManagement } from '../encampment-management/js/index.js';
import { AddressManagement } from '../address-management/js/index.js';
import { AIModule } from '../ai/js/index.js';
import { IncidentManagement } from '../incident-management/js/index.js';
import { VehicleManagement } from '../vehicle-management/js/index.js';
import { PeopleManagement } from '../people-management/js/index.js';
import { ActivityManagement } from '../activity-management/js/index.js';
import { OrganizationManagement } from '../organizations-management/js/index.js';
import { OutreachManagement } from '../outreach-management/js/index.js';
import { ReportsManagement } from '../reports-management/js/index.js';
import { SystemSettings } from '../system-settings/js/index.js';
import { NarrativeManagement } from '../narrative-management/js/index.js';
import { JusticeModule } from '../justice/js/index.js';

const logger = Logger.forModule('ModuleRegistration');

/**
 * Register all application modules with the central registry
 * This should be called once during application startup
 */
export function registerAllModules() {
    logger.info('Registering all application modules...');
    
    try {
        // Register all feature modules
        moduleRegistry.register('PropertyManagement', PropertyManagement);
        moduleRegistry.register('BikeManagement', BikeManagement);
        moduleRegistry.register('EncampmentManagement', EncampmentManagement);
        moduleRegistry.register('AddressManagement', AddressManagement);
        moduleRegistry.register('AIModule', AIModule);
        moduleRegistry.register('IncidentManagement', IncidentManagement);
        moduleRegistry.register('VehicleManagement', VehicleManagement);
        moduleRegistry.register('PeopleManagement', PeopleManagement);
        moduleRegistry.register('ActivityManagement', ActivityManagement);
        moduleRegistry.register('OrganizationManagement', OrganizationManagement);
        moduleRegistry.register('OutreachManagement', OutreachManagement);
        moduleRegistry.register('ReportsManagement', ReportsManagement);
        moduleRegistry.register('SystemSettings', SystemSettings);
        moduleRegistry.register('NarrativeManagement', NarrativeManagement);
        moduleRegistry.register('JusticeModule', JusticeModule);
        
        const registeredCount = moduleRegistry.getRegisteredModules().length;
        logger.info(`Successfully registered ${registeredCount} modules`);
        
    } catch (error) {
        logger.error('Failed to register modules', error);
        throw error;
    }
}

/**
 * Initialize all registered modules with dependencies
 * @param {Object} dependencies - Dependency injection object
 */
export async function initializeAllModules(dependencies) {
    logger.info('Initializing all registered modules...');
    
    try {
        await moduleRegistry.initializeAll(dependencies);
        
        const initializedCount = moduleRegistry.getInitializedModules().length;
        logger.info(`Successfully initialized ${initializedCount} modules`);
        
    } catch (error) {
        logger.error('Failed to initialize modules', error);
        throw error;
    }
}

/**
 * Get module instance from registry
 * @param {string} moduleName - Name of the module
 * @returns {ModuleInterface|null} Module instance or null
 */
export function getModule(moduleName) {
    return moduleRegistry.get(moduleName);
}

/**
 * Get all commands from all registered modules
 * @param {Object} commandManager - Command manager instance
 * @returns {Map} Map of all commands
 */
export function getAllCommands(commandManager) {
    return moduleRegistry.getAllCommands(commandManager);
}

/**
 * Cleanup all modules
 */
export async function cleanupAllModules() {
    logger.info('Cleaning up all modules...');
    
    try {
        await moduleRegistry.cleanupAll();
        logger.info('Successfully cleaned up all modules');
        
    } catch (error) {
        logger.error('Failed to cleanup modules', error);
        throw error;
    }
}
