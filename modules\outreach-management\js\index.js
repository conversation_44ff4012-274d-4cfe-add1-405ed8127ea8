import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { OutreachManager } from './outreach-manager.js';
import { Logger } from '../../shared/logger.js';

export class OutreachManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities = null, modalManagement = null) {
        super('OutreachManagement', '1.0.0', [], ['outreach_transactions', 'outreach_items']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;
        this.logger = Logger.forModule('OutreachManagement');

        // Initialize managers
        this.outreachManager = new OutreachManager(dataManager, authManager, uiManager);
    }

    /**
     * Initialize the outreach management module
     * @returns {Promise<void>}
     */
    async initialize() {
        this.logger.info('Initializing Outreach Management module');
        this.initialized = true;
    }

    /**
     * Cleanup module resources
     * @returns {Promise<void>}
     */
    async cleanup() {
        this.logger.info('Cleaning up Outreach Management module');
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement outreach management commands
        return new Map();
    }
}

export { OutreachManager };
