// Command System for S.T.E.V.I Retro Electron App
import { moduleRegistry } from '../../modules/shared/module-registry.js';
import { outreachFormTemplates } from '../../modules/outreach-management/templates/index.js';

export class CommandManager {
    constructor(app) {
        this.app = app;
        this.auth = app.auth;
        this.data = app.data;
        this.ui = app.ui;
        
        this.commands = new Map();
        this.initializeCommands();
        this.commands = moduleRegistry.getAllCommands(this);
    }

    initializeCommands() {
        // Register all commands
        // report-incident now handled directly in app.js as a page-based form
        
        // Incident commands are now provided by the IncidentManagement module
        // Note: registerIncidentCommands() is called later after incident management is initialized
        
        // People management commands are now handled by PeopleManagement module
        // People management commands now handled by PeopleManagement module
        this.commands.set('add-address', new AddAddressCommand(this));
        this.commands.set('search-addresses', new SearchAddressesCommand(this));
        this.commands.set('manage-addresses', new ManageAddressesCommand(this));
        this.commands.set('view-address-detail', new ViewAddressDetailCommand(this));
        this.commands.set('edit-address', new EditAddressCommand(this));
        this.commands.set('add-address-activity', new AddAddressActivityCommand(this));
        this.commands.set('edit-address-activity', new EditAddressActivityCommand(this));
        this.commands.set('delete-address-activity', new DeleteAddressActivityCommand(this));
        this.commands.set('back-to-address-list', new BackToAddressListCommand(this));
        this.commands.set('search-addresses-input', new SearchAddressesInputCommand(this));
        this.commands.set('cancel-address-form', new CancelAddressFormCommand(this));
        this.commands.set('add-organization', new AddOrganizationCommand(this));
        this.commands.set('search-organizations', new SearchOrganizationsCommand(this));
        this.commands.set('list-organizations', new ListOrganizationsCommand(this));
        this.commands.set('view-organization-detail', new ViewOrganizationDetailCommand(this));
        this.commands.set('edit-organization', new EditOrganizationCommand(this));
        this.commands.set('add-organization-form', new AddOrganizationFormCommand(this));
        this.commands.set('back-to-organization-list', new BackToOrganizationListCommand(this));
        this.commands.set('back-to-organization-detail', new BackToOrganizationDetailCommand(this));
        this.commands.set('save-organization', new SaveOrganizationCommand(this));
        this.commands.set('add-plate', new AddPlateCommand(this));
        this.commands.set('edit-vehicle', new EditVehicleCommand(this));
        this.commands.set('back-to-vehicle-list', new BackToVehicleListCommand(this));
        this.commands.set('search-vehicles-input', new SearchVehiclesInputCommand(this));
        this.commands.set('add-activity', new AddActivityCommand(this));
        this.commands.set('add-person-activity', new AddPersonActivityCommand(this));
        this.commands.set('add-infection-tracking', new AddInfectionTrackingCommand(this));
        this.commands.set('add-mental-health-note', new AddMentalHealthNoteCommand(this));
        this.commands.set('add-addiction-note', new AddAddictionNoteCommand(this));
        this.commands.set('view-weather-alerts', new ViewWeatherAlertsCommand(this));
        this.commands.set('search-records', new SearchRecordsCommand(this));
        this.commands.set('items', new ItemsCommand(this));
        this.commands.set('generate-report', new GenerateReportCommand(this));
        this.commands.set('export-data', new ExportDataCommand(this));
        this.commands.set('sync-data', new SyncDataCommand(this));
        this.commands.set('settings', new SettingsCommand(this));
        this.commands.set('about', new AboutCommand(this));
        this.commands.set('emergency-contacts', new EmergencyContactsCommand(this));
        this.commands.set('user-profile', new UserProfileCommand(this));
        this.commands.set('check-updates', new CheckUpdatesCommand(this));
        this.commands.set('update', new UpdateCommand(this));
        this.commands.set('release-notes', new ReleaseNotesCommand(this));
        this.commands.set('cleanup-updates', new CleanupUpdatesCommand(this));
        this.commands.set('outreach-transactions', new OutreachTransactionCommand(this));
        this.commands.set('delete-outreach-transaction', new DeleteOutreachTransactionCommand(this));
        this.commands.set('log-property', new LogPropertyCommand(this));
        this.commands.set('property-report', new PropertyReportCommand(this));
        this.commands.set('enhanced-property-return', new EnhancedPropertyReturnCommand(this));
        this.commands.set('clear-data', new ClearDataCommand(this));
        this.commands.set('reset-db', new ResetDatabaseCommand(this));
        this.commands.set('sync-schemas', new SyncSchemasCommand(this));
        this.commands.set('schema-report', new SchemaReportCommand(this));
        this.commands.set('add-case-management', new AddCaseManagementCommand(this));
        this.commands.set('add-service-barrier', new AddServiceBarrierCommand(this));
        this.commands.set('add-support-contact', new AddSupportContactCommand(this));
        this.commands.set('add-disability', new AddDisabilityCommand(this));
        // Delete commands for person-related tables
        this.commands.set('delete-case-management', new DeleteCaseManagementCommand(this));
        this.commands.set('delete-service-barrier', new DeleteServiceBarrierCommand(this));
        this.commands.set('delete-support-contact', new DeleteSupportContactCommand(this));
        this.commands.set('delete-disability', new DeleteDisabilityCommand(this));
    }

    registerIncidentCommands() {
        // Get incident commands from the IncidentManagement module
        if (this.app.incidentManagement) {
            console.log('Registering incident commands...');
            const incidentCommands = this.app.incidentManagement.getCommands(this);
            console.log('Incident commands received:', Object.keys(incidentCommands));

            // Register each incident command
            Object.entries(incidentCommands).forEach(([commandName, command]) => {
                console.log(`Registering incident command: ${commandName}`);
                this.commands.set(commandName, command);
            });

            console.log('Incident commands registered successfully');
        } else {
            console.warn('IncidentManagement module not available for command registration');
        }
    }

    registerVehicleCommands() {
        // Get vehicle commands from the VehicleManagement module
        if (this.app.vehicleManagement) {
            const vehicleCommands = this.app.vehicleManagement.getCommands(this);
            
            // Register each vehicle command
            Object.entries(vehicleCommands).forEach(([commandName, command]) => {
                this.commands.set(commandName, command);
                console.log(`✅ Registered vehicle command: ${commandName}`);
            });
            console.log(`✅ Vehicle commands registered: ${Object.keys(vehicleCommands).length} commands`);
        } else {
            console.error('❌ vehicleManagement not available when trying to register commands');
        }
    }

    /**
     * Register vehicle commands after vehicleManagement is initialized
     */

    /**
     * Register people commands from the PeopleManagement module
     */
    registerPeopleCommands() {
        // Get people commands from the PeopleManagement module
        if (this.app.peopleManagement) {
            const peopleCommands = this.app.peopleManagement.getCommands(this);
            
            // Register each people command
            Object.entries(peopleCommands).forEach(([commandName, command]) => {
                this.commands.set(commandName, command);
                console.log(`✅ Registered people command: ${commandName}`);
            });
            console.log(`✅ People commands registered: ${Object.keys(peopleCommands).length} commands`);
        } else {
            console.warn('⚠️ PeopleManagement not available for command registration');
        }
    }

    /**
     * Register people commands after peopleManagement is initialized
     */

    /**
     * Register encampment commands from the EncampmentManagement module
     */
    registerEncampmentCommands() {
        // Get encampment commands from the EncampmentManagement module
        if (this.app.encampmentManagement) {
            const encampmentCommands = this.app.encampmentManagement.getCommands(this);
            
            // Register each encampment command
            Object.entries(encampmentCommands).forEach(([commandName, command]) => {
                this.commands.set(commandName, command);
                console.log(`✅ Registered encampment command: ${commandName}`);
            });
            console.log(`✅ Encampment commands registered: ${Object.keys(encampmentCommands).length} commands`);
        } else {
            console.warn('⚠️ EncampmentManagement not available for command registration');
        }
    }

    /**
     * Register encampment commands after encampmentManagement is initialized
     */

    /**
     * Register incident commands after incidentManagement is initialized
     */


    async executeCommand(commandName, args = []) {
        const command = this.commands.get(commandName);
        if (!command) {
            console.error(`Unknown command: ${commandName}`);
            console.log('Available commands:', Array.from(this.commands.keys()).sort());
            throw new Error(`Unknown command: ${commandName}`);
        }

        try {
            return await command.execute(args);
        } catch (error) {
            console.error(`Error executing command ${commandName}:`, error);
            this.ui.showDialog('Error', error.message, 'error');
            throw error;
        }
    }

    getCommand(commandName) {
        return this.commands.get(commandName);
    }

    getAllCommands() {
        return Array.from(this.commands.keys());
    }
}

// Base Command Class
class BaseCommand {
    constructor(manager) {
        this.manager = manager;
        this.auth = manager.auth;
        this.data = manager.data;
        this.ui = manager.ui;
        this.app = manager.app;
        this.schema = manager.app.schema;
    }

    async execute(args) {
        throw new Error('Command must implement execute method');
    }
}


// People management commands moved to PeopleManagement module

// Search Records Command - correct version below
/* CORRUPTED CLASS REMOVED - Real SearchRecordsCommand follows below */

class SearchRecordsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showSearchInterface(resolve);
        });
    }

    showSearchInterface(resolve) {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Search Records</h3>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="search-type">Search In:</label>
                            <select id="search-type" name="search-type">
                                <option value="all">All Records</option>
                                <option value="people">People</option>
                                <option value="addresses">Addresses</option>
                                <option value="license_plates">License Plates</option>
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="search-query">Search For:</label>
                            <input type="text" id="search-query" name="search-query" placeholder="Enter name, address, plate number, etc.">
                        </div>
                        <div class="form-field">
                            <label for="search-field">Search Field:</label>
                            <select id="search-field" name="search-field">
                                <option value="all">All Fields</option>
                                <option value="first_name">First Name</option>
                                <option value="last_name">Last Name</option>
                                <option value="email">Email</option>
                                <option value="phone">Phone</option>
                                <option value="street_address">Street Address</option>
                                <option value="city">City</option>
                                <option value="plate_number">Plate Number</option>
                                <option value="vehicle_make">Vehicle Make</option>
                                <option value="vehicle_model">Vehicle Model</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="secondary-button" id="search-cancel">Cancel</button>
                            <button type="button" class="primary-button" id="search-submit">Search</button>
                        </div>
                    </div>
                    <div class="search-results" id="search-results" style="display: none;">
                        <h4>Search Results</h4>
                        <div class="results-container" id="results-container"></div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        // Set up event handlers
        this.setupSearchHandlers(searchModal, resolve);

        // Focus search input
        const searchInput = searchModal.querySelector('#search-query');
        searchInput.focus();
    }

    setupSearchHandlers(modal, resolve) {
        const searchType = modal.querySelector('#search-type');
        const searchField = modal.querySelector('#search-field');
        const searchQuery = modal.querySelector('#search-query');
        const searchSubmit = modal.querySelector('#search-submit');
        const searchCancel = modal.querySelector('#search-cancel');
        const searchResults = modal.querySelector('#search-results');
        const resultsContainer = modal.querySelector('#results-container');

        // Update available fields based on search type
        searchType.addEventListener('change', () => {
            this.updateSearchFields(searchType.value, searchField);
        });

        // Handle search submission
        const performSearch = async () => {
            const type = searchType.value;
            const query = searchQuery.value.trim();
            const field = searchField.value;

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.performSearch(type, query, field);
                this.displayResults(results, resultsContainer, searchResults);

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        // Event listeners
        searchSubmit.addEventListener('click', performSearch);
        searchCancel.addEventListener('click', () => {
            modal.remove();
            resolve(null);
        });

        // Enter key to search
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Escape key to cancel
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                modal.remove();
                resolve(null);
            }
        });
    }

    updateSearchFields(searchType, searchField) {
        // Clear current options
        searchField.innerHTML = '<option value="all">All Fields</option>';

        const fieldOptions = {
            people: [
                { value: 'first_name', text: 'First Name' },
                { value: 'last_name', text: 'Last Name' },
                { value: 'email', text: 'Email' },
                { value: 'phone', text: 'Phone' },
                { value: 'emergency_contact', text: 'Emergency Contact' }
            ],
            addresses: [
                { value: 'street_address', text: 'Street Address' },
                { value: 'city', text: 'City' },
                { value: 'province', text: 'Province' },
                { value: 'postal_code', text: 'Postal Code' }
            ],
            license_plates: [
                { value: 'plate_number', text: 'Plate Number' },
                { value: 'province', text: 'Province' },
                { value: 'vehicle_make', text: 'Vehicle Make' },
                { value: 'vehicle_model', text: 'Vehicle Model' },
                { value: 'vehicle_color', text: 'Vehicle Color' },
                { value: 'owner_name', text: 'Owner Name' }
            ]
        };

        if (searchType === 'all') {
            // Add all fields for all types
            Object.values(fieldOptions).forEach(fields => {
                fields.forEach(field => {
                    const option = document.createElement('option');
                    option.value = field.value;
                    option.textContent = field.text;
                    searchField.appendChild(option);
                });
            });
        } else if (fieldOptions[searchType]) {
            fieldOptions[searchType].forEach(field => {
                const option = document.createElement('option');
                option.value = field.value;
                option.textContent = field.text;
                searchField.appendChild(option);
            });
        }
    }

    async performSearch(type, query, field) {
        const results = {
            people: [],
            addresses: [],
            license_plates: []
        };

        const searchTables = type === 'all' ? ['people', 'addresses', 'license_plates'] : [type];

        for (const table of searchTables) {
            try {
                let searchCriteria = {};

                if (field === 'all') {
                    // Search across multiple fields for the table
                    const tableResults = await this.searchAllFields(table, query);
                    results[table] = tableResults;
                } else {
                    // Search specific field
                    searchCriteria[field] = query;
                    const tableResults = await this.data.search(table, searchCriteria);
                    results[table] = tableResults || [];
                }
            } catch (error) {
                console.error(`Error searching ${table}:`, error);
                results[table] = [];
            }
        }

        return results;
    }

    async searchAllFields(table, query) {
        try {
            // Get all records and filter client-side for comprehensive search
            const allRecords = await this.data.search(table, {});

            if (!allRecords || allRecords.length === 0) {
                return [];
            }

            const lowerQuery = query.toLowerCase();

            return allRecords.filter(record => {
                // Search across all string fields
                return Object.values(record).some(value => {
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(lowerQuery);
                    }
                    return false;
                });
            });
        } catch (error) {
            console.error(`Error in searchAllFields for ${table}:`, error);
            return [];
        }
    }

    displayResults(results, container, resultsSection) {
        container.innerHTML = '';

        let totalResults = 0;
        Object.values(results).forEach(tableResults => {
            totalResults += tableResults.length;
        });

        if (totalResults === 0) {
            container.innerHTML = '<div class="no-results">No records found matching your search criteria.</div>';
            resultsSection.style.display = 'block';
            return;
        }

        // Display results by category
        Object.entries(results).forEach(([table, records]) => {
            if (records.length > 0) {
                const section = document.createElement('div');
                section.className = 'results-section';

                const header = document.createElement('h5');
                header.textContent = `${this.formatTableName(table)} (${records.length})`;
                section.appendChild(header);

                const recordsList = document.createElement('div');
                recordsList.className = 'records-list';

                records.forEach(record => {
                    const recordDiv = document.createElement('div');
                    recordDiv.className = 'record-item';
                    recordDiv.innerHTML = this.formatRecord(table, record);

                    // Add click handler to open record detail view
                    recordDiv.addEventListener('click', () => {
                        this.openRecordDetail(table, record);
                    });

                    recordsList.appendChild(recordDiv);
                });

                section.appendChild(recordsList);
                container.appendChild(section);
            }
        });

        resultsSection.style.display = 'block';
    }

    formatTableName(table) {
        const names = {
            people: 'People',
            addresses: 'Addresses',
            license_plates: 'License Plates'
        };
        return names[table] || table;
    }

    formatRecord(table, record) {
        switch (table) {
            case 'people':
                return `
                    <div class="record-header">${record.first_name || ''} ${record.last_name || ''}</div>
                    <div class="record-details">
                        ${record.email ? `<span>Email: ${record.email}</span>` : ''}
                        ${record.phone ? `<span>Phone: ${record.phone}</span>` : ''}
                        ${record.housing_status ? `<span>Housing: ${record.housing_status}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            case 'addresses':
                return `
                    <div class="record-header">${record.street_address || ''}</div>
                    <div class="record-details">
                        <span>${record.city || ''}, ${record.province || ''} ${record.postal_code || ''}</span>
                        ${record.country ? `<span>Country: ${record.country}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            case 'license_plates':
                return `
                    <div class="record-header">${record.plate_number || ''} (${record.province || ''})</div>
                    <div class="record-details">
                        ${record.vehicle_make ? `<span>${record.vehicle_make} ${record.vehicle_model || ''}</span>` : ''}
                        ${record.vehicle_year ? `<span>Year: ${record.vehicle_year}</span>` : ''}
                        ${record.vehicle_color ? `<span>Color: ${record.vehicle_color}</span>` : ''}
                        ${record.owner_name ? `<span>Owner: ${record.owner_name}</span>` : ''}
                    </div>
                    <div class="record-meta">ID: ${record.id} | Created: ${this.formatDate(record.created_at)}</div>
                `;

            default:
                return `<div class="record-header">Record ID: ${record.id}</div>`;
        }
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown';
        try {
            return new Date(dateString).toLocaleDateString();
        } catch {
            return dateString;
        }
    }

    openRecordDetail(table, record) {
        try {
            console.log('Opening record detail for:', table, record);

            // Close search modal first
            const searchModal = document.querySelector('.modal-overlay');
            if (searchModal) {
                searchModal.remove();
            }

            // Handle people records differently - show summary view first
            if (table === 'people') {
                this.showPersonSummaryView(record);
            } else {
                // For other record types, use the old modal system
                this.showRecordDetailView(table, record);
            }
        } catch (error) {
            console.error('Error opening record detail:', error);
            console.error('Error stack:', error.stack);
            this.ui.showDialog('Error', `Failed to open record: ${error.message}`, 'error');
        }
    }

    showPersonSummaryView(person) {
        try {
            const summaryModal = document.createElement('div');
            summaryModal.className = 'modal-overlay';

            summaryModal.innerHTML = `
                <div class="modal-dialog person-summary-modal">
                    <div class="modal-header">
                        <h3>Person Summary</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="person-summary-container">
                            <div class="person-summary-header">
                                <div class="person-avatar-summary">
                                    <div class="avatar-placeholder-summary">
                                        ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                                    </div>
                                </div>
                                <div class="person-summary-info">
                                    <h4 class="person-summary-name">
                                        ${person.first_name || ''} ${person.last_name || ''}
                                    </h4>
                                    <div class="person-summary-id">ID: ${person.id}</div>
                                </div>
                            </div>

                            <div class="person-summary-details">
                                <div class="summary-section">
                                    <h5>Contact Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Email:</span>
                                            <span class="summary-value">${person.email || 'Not provided'}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Phone:</span>
                                            <span class="summary-value">${person.phone || 'Not provided'}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="summary-section">
                                    <h5>Personal Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Date of Birth:</span>
                                            <span class="summary-value">${person.date_of_birth || 'Not provided'}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Housing Status:</span>
                                            <span class="summary-value">${person.housing_status || 'Not provided'}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="summary-section">
                                    <h5>Record Information</h5>
                                    <div class="summary-grid">
                                        <div class="summary-item">
                                            <span class="summary-label">Created:</span>
                                            <span class="summary-value">${this.formatDate(person.created_at)}</span>
                                        </div>
                                        <div class="summary-item">
                                            <span class="summary-label">Created By:</span>
                                            <span class="summary-value">${person.created_by || 'System'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" id="view-full-record-btn">View Full Record</button>
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                    </div>
                </div>
            `;

            document.body.appendChild(summaryModal);

            // Handle "View Full Record" button click
            const viewFullRecordBtn = summaryModal.querySelector('#view-full-record-btn');
            viewFullRecordBtn.addEventListener('click', async () => {
                summaryModal.remove();

                // Navigate to the full person detail page
                if (window.app) {
                    await window.app.viewPersonDetail(person.id);
                }
            });

        } catch (error) {
            console.error('Error showing person summary view:', error);
            this.ui.showDialog('Error', `Failed to show person summary: ${error.message}`, 'error');
        }
    }

    showRecordDetailView(table, record) {
        try {
            console.log('Creating record detail view for:', table, record);

            const detailModal = document.createElement('div');
            detailModal.className = 'modal-overlay';

        detailModal.innerHTML = `
            <div class="modal-dialog record-detail-modal">
                <div class="modal-header">
                    <h3>${this.formatTableName(table)} - ${this.getRecordTitle(table, record)}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="record-view-container">
                        <!-- Profile Picture Section -->
                        <div class="profile-section">
                            <div class="profile-picture">
                                <div class="ascii-avatar">
┌─────────────┐
│             │
│    ┌───┐    │
│   ╱     ╲   │
│  │  ● ●  │  │
│  │   ▽   │  │
│   ╲ \_/ ╱   │
│    └───┘    │
│      │      │
│   ┌──┴──┐   │
│   │     │   │
│   └─────┘   │
└─────────────┘
                                </div>
                                <div class="profile-name">${this.getRecordTitle(table, record)}</div>
                            </div>
                            <div class="record-actions">
                                <button class="primary-button" id="edit-record-btn">
                                    <span class="edit-icon">✏️</span> Edit Record
                                </button>
                                <button class="primary-button" id="add-activity-btn">
                                    <span class="activity-icon">📝</span> Add Activity
                                </button>
                            </div>
                        </div>

                        <!-- Record Details Section -->
                        <div class="details-section">
                            <h4>Record Details</h4>
                            <div class="record-details-view" id="record-details">
                                ${this.formatEditableRecordDetails(table, record)}
                            </div>
                        </div>

                        <!-- Activity Log Section -->
                        <div class="activities-section">
                            <h4>Activity Log</h4>
                            <div class="activities-list" id="activities-list">
                                Loading activities...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(detailModal);

        // Set up event handlers
        this.setupRecordDetailHandlers(detailModal, table, record);

        // Load activities
        this.loadRecordActivities(table, record.id);

        } catch (error) {
            console.error('Error showing record detail view:', error);
            console.error('Error stack:', error.stack);
            this.ui.showDialog('Error', `Failed to show record details: ${error.message}`, 'error');
        }
    }

    getActivityTableName(recordTable) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities',
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordTable] || 'activities';
    }

    getRecordTitle(table, record) {
        switch (table) {
            case 'people':
                return `${record.first_name || ''} ${record.last_name || ''}`.trim() || 'Unknown Person';
            case 'addresses':
                return record.street_address || 'Unknown Address';
            case 'license_plates':
                return `${record.plate_number || ''} (${record.province || ''})`.trim() || 'Unknown Vehicle';
            default:
                return `Record ${record.id}`;
        }
    }

    formatRecordDetails(table, record) {
        const details = [];

        Object.entries(record).forEach(([key, value]) => {
            if (key !== 'id' && value !== null && value !== undefined && value !== '') {
                const label = this.formatFieldLabel(key);
                const formattedValue = this.formatFieldValue(key, value);
                details.push(`
                    <div class="detail-row">
                        <span class="detail-label">${label}:</span>
                        <span class="detail-value">${formattedValue}</span>
                    </div>
                `);
            }
        });

        return details.join('');
    }

    formatEditableRecordDetails(table, record) {
        try {
            console.log('Formatting editable record details for:', table, record);

            const details = [];

            // Get schema for this table to determine field types
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            const schema = this.schema.getTableSchema(table);
            if (!schema) {
                throw new Error(`Schema not found for table: ${table}`);
            }

            console.log('Schema loaded:', schema);

            if (!record || typeof record !== 'object') {
                throw new Error('Invalid record object');
            }

        Object.entries(record).forEach(([key, value]) => {
            if (key !== 'id' && key !== 'created_at' && key !== 'updated_at' && key !== 'created_by') {
                const label = this.formatFieldLabel(key);
                const fieldSchema = schema[key];
                const displayValue = this.formatFieldValue(key, value);

                details.push(`
                    <div class="detail-row" data-field="${key}">
                        <span class="detail-label">${label}:</span>
                        <span class="detail-value" data-original-value="${value || ''}">${displayValue || '<em>Not set</em>'}</span>
                        <span class="edit-controls" style="display: none;">
                            ${this.createInlineEditField(key, value, fieldSchema)}
                            <button class="save-field-btn" data-field="${key}">✓</button>
                            <button class="cancel-field-btn" data-field="${key}">✗</button>
                        </span>
                    </div>
                `);
            }
        });

        return details.join('');

        } catch (error) {
            console.error('Error formatting editable record details:', error);
            console.error('Error stack:', error.stack);
            return `<div class="error">Error loading record details: ${error.message}</div>`;
        }
    }

    createInlineEditField(fieldName, value, fieldSchema) {
        const fieldType = fieldSchema?.type || 'text';

        if (fieldType === 'boolean') {
            const checked = value ? 'checked' : '';
            return `<input type="checkbox" class="edit-input" data-field="${fieldName}" ${checked}>`;
        } else if (fieldType === 'date') {
            const dateValue = value ? this.ui.formatDateForInput(value) : '';
            return this.ui.createCustomDateInput(fieldName + '_edit', dateValue, '');
        } else if (fieldType === 'time') {
            return `<input type="time" class="edit-input" data-field="${fieldName}" value="${value || ''}">`;
        } else if (fieldName.includes('description') || fieldName.includes('notes')) {
            return `<textarea class="edit-input" data-field="${fieldName}" rows="2">${value || ''}</textarea>`;
        } else {
            return `<input type="text" class="edit-input" data-field="${fieldName}" value="${value || ''}">`;
        }
    }

    formatFieldLabel(fieldName) {
        return fieldName
            .replace(/_/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
    }

    formatFieldValue(fieldName, value) {
        // Handle null, undefined, or empty values
        if (value === null || value === undefined || value === '') {
            return '';
        }

        if (fieldName.includes('date') && value) {
            try {
                return new Date(value).toLocaleDateString();
            } catch {
                return value;
            }
        }

        if (fieldName.includes('time') && value) {
            return value;
        }

        if (typeof value === 'boolean') {
            return value ? 'Yes' : 'No';
        }

        // Safely convert to string
        return String(value);
    }

    setupRecordDetailHandlers(modal, table, record) {
        const editRecordBtn = modal.querySelector('#edit-record-btn');
        const addActivityBtn = modal.querySelector('#add-activity-btn');

        // Edit record button - toggles edit mode
        if (editRecordBtn) {
            editRecordBtn.addEventListener('click', () => {
                this.toggleEditMode(modal, table, record);
            });
        }

        // Add activity button
        if (addActivityBtn) {
            addActivityBtn.addEventListener('click', () => {
                this.showAddActivityForm(table, record);
            });
        }

        // Set up field-level edit handlers
        this.setupFieldEditHandlers(modal, table, record);
    }

    toggleEditMode(modal, table, record) {
        const editBtn = modal.querySelector('#edit-record-btn');
        const detailRows = modal.querySelectorAll('.detail-row');
        const isEditing = editBtn.textContent.includes('Cancel');

        if (isEditing) {
            // Cancel edit mode
            editBtn.innerHTML = '<span class="edit-icon">✏️</span> Edit Record';
            detailRows.forEach(row => {
                const valueSpan = row.querySelector('.detail-value');
                const editControls = row.querySelector('.edit-controls');
                if (valueSpan && editControls) {
                    valueSpan.style.display = 'inline';
                    editControls.style.display = 'none';
                }
            });
        } else {
            // Enter edit mode
            editBtn.innerHTML = '<span class="edit-icon">✗</span> Cancel Edit';
            detailRows.forEach(row => {
                const valueSpan = row.querySelector('.detail-value');
                const editControls = row.querySelector('.edit-controls');
                if (valueSpan && editControls) {
                    valueSpan.style.display = 'none';
                    editControls.style.display = 'inline-flex';
                }
            });
        }
    }

    setupFieldEditHandlers(modal, table, record) {
        // Save field buttons
        modal.addEventListener('click', async (e) => {
            if (e.target.classList.contains('save-field-btn')) {
                const fieldName = e.target.dataset.field;
                await this.saveFieldEdit(modal, table, record, fieldName);
            }

            if (e.target.classList.contains('cancel-field-btn')) {
                const fieldName = e.target.dataset.field;
                this.cancelFieldEdit(modal, fieldName);
            }
        });

        // Set up custom date inputs
        this.ui.setupCustomDateInputs(modal);
    }

    async saveFieldEdit(modal, table, record, fieldName) {
        try {
            const detailRow = modal.querySelector(`[data-field="${fieldName}"]`);
            const editInput = detailRow.querySelector('.edit-input');
            let newValue;

            if (editInput.type === 'checkbox') {
                newValue = editInput.checked;
            } else if (editInput.type === 'hidden' && fieldName.includes('date')) {
                // Custom date input
                newValue = editInput.value;
            } else {
                newValue = editInput.value;
            }

            // Update the record
            await this.data.update(table, record.id, { [fieldName]: newValue });

            // Update the display
            const valueSpan = detailRow.querySelector('.detail-value');
            const formattedValue = this.formatFieldValue(fieldName, newValue);
            valueSpan.textContent = formattedValue || 'Not set';
            valueSpan.dataset.originalValue = newValue;

            // Update the record object
            record[fieldName] = newValue;

            // Hide edit controls, show value
            const editControls = detailRow.querySelector('.edit-controls');
            valueSpan.style.display = 'inline';
            editControls.style.display = 'none';

            this.ui.showDialog('Success', 'Field updated successfully!', 'success');

        } catch (error) {
            console.error('Error saving field:', error);
            this.ui.showDialog('Error', `Failed to update field: ${error.message}`, 'error');
        }
    }

    cancelFieldEdit(modal, fieldName) {
        const detailRow = modal.querySelector(`[data-field="${fieldName}"]`);
        const valueSpan = detailRow.querySelector('.detail-value');
        const editControls = detailRow.querySelector('.edit-controls');
        const editInput = detailRow.querySelector('.edit-input');

        // Restore original value
        const originalValue = valueSpan.dataset.originalValue;
        if (editInput.type === 'checkbox') {
            editInput.checked = originalValue === 'true';
        } else {
            editInput.value = originalValue;
        }

        // Hide edit controls, show value
        valueSpan.style.display = 'inline';
        editControls.style.display = 'none';
    }

    async loadRecordActivities(table, recordId) {
        try {
            console.log(`Loading activities for ${table} record ${recordId}`);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            const activities = await this.data.search(activityTable, {
                [foreignKey]: recordId
            });

            console.log(`Found ${activities ? activities.length : 0} activities`);

            const activitiesList = document.getElementById('activities-list');
            if (!activitiesList) {
                console.error('Activities list element not found');
                return;
            }

            if (!activities || activities.length === 0) {
                activitiesList.innerHTML = '<div class="no-activities">No activities recorded yet.</div>';
                return;
            }

            // Sort activities by date (newest first)
            activities.sort((a, b) => {
                const dateA = new Date(a.activity_date + ' ' + (a.activity_time || '00:00'));
                const dateB = new Date(b.activity_date + ' ' + (b.activity_time || '00:00'));
                return dateB - dateA;
            });

            const activitiesHTML = activities.map(activity => this.formatActivity(activity)).join('');
            activitiesList.innerHTML = activitiesHTML;

            // Load supply provision details for supply provision activities
            const supplyActivities = activities.filter(activity => activity.activity_type === 'supply_provision');
            for (const activity of supplyActivities) {
                await this.loadSupplyProvisionDetails(activity.id);
            }

        } catch (error) {
            console.error('Error loading activities:', error);
            console.error('Error stack:', error.stack);
            const activitiesList = document.getElementById('activities-list');
            if (activitiesList) {
                activitiesList.innerHTML = `<div class="error">Failed to load activities: ${error.message}</div>`;
            }
        }
    }

    async loadSupplyProvisionDetails(activityId) {
        try {
            // Get supply provisions for this activity
            const provisions = await this.data.search('supply_provisions', {
                activity_id: activityId
            });

            if (!provisions || provisions.length === 0) {
                const suppliesElement = document.getElementById(`supplies-${activityId}`);
                if (suppliesElement) {
                    suppliesElement.innerHTML = `
                        <h5>📦 Supplies Provided:</h5>
                        <div class="no-supplies">No supply details found.</div>
                    `;
                }
                return;
            }

            // Get item details for each provision
            const suppliesWithDetails = [];
            for (const provision of provisions) {
                const item = await this.data.get('items', provision.item_id);
                if (item) {
                    suppliesWithDetails.push({
                        ...provision,
                        item_name: item.name,
                        unit_type: item.unit_type,
                        category: item.category
                    });
                }
            }

            // Generate supplies HTML
            const suppliesHTML = suppliesWithDetails.map(supply => `
                <li>
                    <span class="supply-quantity">${supply.quantity_provided}</span>
                    ${supply.unit_type} of
                    <strong>${supply.item_name}</strong>
                    <span class="supply-category">(${supply.category.replace('_', ' ')})</span>
                </li>
            `).join('');

            // Update the supplies element
            const suppliesElement = document.getElementById(`supplies-${activityId}`);
            if (suppliesElement) {
                suppliesElement.innerHTML = `
                    <h5>📦 Supplies Provided:</h5>
                    <ul class="supply-list">
                        ${suppliesHTML}
                    </ul>
                `;
            }

        } catch (error) {
            console.error('Error loading supply provision details:', error);
            const suppliesElement = document.getElementById(`supplies-${activityId}`);
            if (suppliesElement) {
                suppliesElement.innerHTML = `
                    <h5>📦 Supplies Provided:</h5>
                    <div class="error">Failed to load supply details.</div>
                `;
            }
        }
    }

    getForeignKeyName(table) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[table] || 'record_id';
    }

    formatActivity(activity) {
        const date = new Date(activity.activity_date).toLocaleDateString();
        const time = activity.activity_time || '';
        const priority = activity.priority ? `<span class="priority priority-${activity.priority}">${activity.priority.toUpperCase()}</span>` : '';

        return `
            <div class="activity-item">
                <div class="activity-header">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-meta">
                        <span class="activity-type">${activity.activity_type.replace('_', ' ').toUpperCase()}</span>
                        ${priority}
                        <span class="activity-date">${date} ${time}</span>
                    </div>
                </div>
                <div class="activity-content">
                    ${activity.description ? `<p>${activity.description}</p>` : ''}
                    ${activity.location ? `<div class="activity-location"><strong>Location:</strong> ${activity.location}</div>` : ''}
                    ${activity.outcome ? `<div class="activity-outcome"><strong>Outcome:</strong> ${activity.outcome}</div>` : ''}
                    ${activity.findings ? `<div class="activity-findings"><strong>Findings:</strong> ${activity.findings}</div>` : ''}
                    ${activity.action_taken ? `<div class="activity-action"><strong>Action Taken:</strong> ${activity.action_taken}</div>` : ''}
                    ${activity.activity_type === 'supply_provision' ? this.formatSupplyProvisionDetails(activity.id) : ''}
                </div>
                <div class="activity-footer">
                    <span class="activity-staff">By: ${activity.staff_member}</span>
                    ${activity.follow_up_required ? `<span class="follow-up-required">Follow-up required: ${activity.follow_up_date || 'TBD'}</span>` : ''}
                </div>
            </div>
        `;
    }

    formatSupplyProvisionDetails(activityId) {
        // This will be populated asynchronously
        return `<div class="activity-supplies" id="supplies-${activityId}">
            <h5>📦 Supplies Provided:</h5>
            <div class="loading-supplies">Loading supply details...</div>
        </div>`;
    }

    showAddActivityForm(table, record) {
        try {
            console.log(`Showing add activity form for ${table} record:`, record);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            // Get current user info
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                [foreignKey]: record.id,
                activity_date: new Date().toISOString().split('T')[0], // Today's date
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            console.log('Default data:', defaultData);

            // Check if schema is available
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            // Generate form fields for the activity table, excluding system fields and foreign keys
            const excludeFields = ['id', 'created_at', 'created_by', foreignKey];
            const fields = this.schema.generateFormFields(activityTable, excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            console.log(`Generated ${fields.length} fields for activity form`);

            // Create and show the form
            this.ui.showFullScreenForm(
                `Add Activity - ${this.getRecordTitle(table, record)}`,
                fields,
                async (formData) => {
                    try {
                        // Check if this is a supply provision activity
                        if (formData.activity_type === 'supply_provision') {
                            const result = await this.handleSupplyProvisionActivity(table, record, formData, defaultData);
                            return result;
                        }

                        // Merge default data with form data
                        const activityData = { ...defaultData, ...formData };

                        await this.data.insert(activityTable, activityData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        // Reload activities list
                        this.loadRecordActivities(table, record.id);

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    async handleSupplyProvisionActivity(table, record, formData, defaultData) {
        try {
            // Show supply selection form
            return await this.showSupplySelectionForm(table, record, formData, defaultData);
        } catch (error) {
            console.error('Error handling supply provision activity:', error);
            this.ui.showDialog('Error', `Failed to handle supply provision: ${error.message}`, 'error');
            return false;
        }
    }

    async showSupplySelectionForm(table, record, activityData, defaultData) {
        try {
            // Force refresh items cache to ensure we have latest data
            await this.forceRefreshItems();
            
            // Get available items
            const items = await this.data.search('items', { active: true });

            if (items.length === 0) {
                this.ui.showDialog('No Items Available', 'No active items found in inventory. Please add items first.', 'warning');
                return false;
            }

            // Create supply selection modal
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';

            const itemsHTML = items.map(item => `
                <div class="supply-item" data-item-id="${item.id}">
                    <div class="supply-item-header">
                        <div class="supply-item-name">${item.name}</div>
                        <div class="supply-item-stock">Stock: ${item.current_stock} ${item.unit_type}</div>
                    </div>
                    <div class="supply-item-description">${item.description || ''}</div>
                    <div class="supply-item-category">${item.category.replace('_', ' ').toUpperCase()}</div>
                    <div class="supply-item-controls">
                        <label>Quantity:</label>
                        <input type="number" class="quantity-input" min="0" max="${item.current_stock}" value="0" data-item-id="${item.id}">
                        <span class="unit-label">${item.unit_type}</span>
                    </div>
                </div>
            `).join('');

            modal.innerHTML = `
                <div class="modal-dialog large-modal">
                    <div class="modal-header">
                        <h3>Supply Provision - ${this.getRecordTitle(table, record)}</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <div class="activity-summary">
                            <p><strong>Activity:</strong> ${activityData.title}</p>
                            <p><strong>Date:</strong> ${activityData.activity_date}</p>
                            <p><strong>Staff:</strong> ${activityData.staff_member}</p>
                            ${activityData.description ? `<p><strong>Description:</strong> ${activityData.description}</p>` : ''}
                        </div>
                        <hr>
                        <h4>Select Items to Provide:</h4>
                        <div class="supply-items-list">
                            ${itemsHTML}
                        </div>
                        <div class="supply-notes">
                            <label for="supply-notes">Additional Notes:</label>
                            <textarea id="supply-notes" placeholder="Any additional notes about the supplies provided..."></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                        <button class="primary-button" id="save-supply-provision">Save Supply Provision</button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // Handle save button
            const saveButton = modal.querySelector('#save-supply-provision');
            saveButton.addEventListener('click', async () => {
                await this.saveSupplyProvision(modal, table, record, activityData, defaultData);
            });

            return true;
        } catch (error) {
            console.error('Error showing supply selection form:', error);
            this.ui.showDialog('Error', `Failed to show supply selection: ${error.message}`, 'error');
            return false;
        }
    }

    async saveSupplyProvision(modal, table, record, activityData, defaultData) {
        try {
            // Get all quantity inputs
            const quantityInputs = modal.querySelectorAll('.quantity-input');
            const selectedItems = [];

            quantityInputs.forEach(input => {
                const quantity = parseInt(input.value) || 0;
                if (quantity > 0) {
                    selectedItems.push({
                        item_id: input.dataset.itemId,
                        quantity: quantity
                    });
                }
            });

            if (selectedItems.length === 0) {
                this.ui.showDialog('No Items Selected', 'Please select at least one item with a quantity greater than 0.', 'warning');
                return;
            }

            // Get additional notes
            const notes = modal.querySelector('#supply-notes').value;

            // Create the activity record first
            const activityTable = this.getActivityTableName(table);
            const mergedActivityData = { ...defaultData, ...activityData };

            // Add supply provision specific notes
            if (notes) {
                mergedActivityData.description = (mergedActivityData.description || '') +
                    (mergedActivityData.description ? '\n\nSupply Notes: ' : 'Supply Notes: ') + notes;
            }

            const activityResult = await this.data.insert(activityTable, mergedActivityData);
            const activityId = activityResult.id;

            // Create supply provision records and update inventory
            for (const item of selectedItems) {
                // Create supply provision record
                await this.data.insert('supply_provisions', {
                    activity_id: activityId,
                    item_id: item.item_id,
                    quantity_provided: item.quantity,
                    notes: notes,
                    created_at: new Date().toISOString()
                });

                // Update item stock
                const currentItem = await this.data.get('items', item.item_id);
                const newStock = currentItem.current_stock - item.quantity;

                await this.data.update('items', item.item_id, {
                    current_stock: newStock,
                    updated_at: new Date().toISOString()
                });
            }

            // Close modal and show success
            modal.remove();
            this.ui.showDialog('Success',
                `Supply provision activity created successfully! ${selectedItems.length} item(s) provided.`,
                'success'
            );

            // Reload activities list
            this.loadRecordActivities(table, record.id);

            return true;
        } catch (error) {
            console.error('Error saving supply provision:', error);
            this.ui.showDialog('Error', `Failed to save supply provision: ${error.message}`, 'error');
            return false;
        }
    }


}



class AddAddressCommand extends BaseCommand {
    async execute(args) {
        // Delegate to app.js to show the custom address form with Google Maps integration
        await this.app.showAddressForm();
    }
}

class SearchAddressesCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                await this.app.showAddressSearchModal();
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to open address search: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class ManageAddressesCommand extends BaseCommand {
    async execute(args) {
        await this.app.loadAddressesManagementContent();
    }
}

class ViewAddressDetailCommand extends BaseCommand {
    async execute(args) {
        const addressId = args.addressId || args[0];
        if (!addressId) {
            this.ui.showDialog('Error', 'No address ID provided', 'error');
            return;
        }
        
        try {
            const address = await this.data.get('addresses', addressId);
            if (!address) {
                this.ui.showDialog('Error', 'Address not found', 'error');
                return;
            }
            await this.app.viewAddressDetail(address);
        } catch (error) {
            this.ui.showDialog('Error', `Failed to load address: ${error.message}`, 'error');
        }
    }
}

class EditAddressCommand extends BaseCommand {
    async execute(args) {
        const addressId = args.addressId || args[0];
        if (!addressId) {
            this.ui.showDialog('Error', 'No address ID provided', 'error');
            return;
        }

        try {
            // Get current address data
            const currentAddress = await this.data.get('addresses', addressId);
            if (!currentAddress) {
                this.ui.showDialog('Error', 'Address not found', 'error');
                return;
            }

            // Delegate to app.js to show the custom address form with pre-populated data
            await this.app.showAddressForm(currentAddress);
        } catch (error) {
            this.ui.showDialog('Error', `Failed to load address: ${error.message}`, 'error');
        }
    }
}

class BackToAddressListCommand extends BaseCommand {
    async execute(args) {
        await this.app.loadAddressesManagementContent();
    }
}

class AddAddressActivityCommand extends BaseCommand {
    async execute(args) {
        const addressId = args.addressId || args[0];
        
        if (!addressId) {
            this.ui.showDialog('Error', 'Address ID is required for adding an activity.', 'error');
            return;
        }

        try {
            // Get the address to show in the form
            const address = await this.data.get('addresses', addressId);
            if (!address) {
                this.ui.showDialog('Error', 'Address not found.', 'error');
                return;
            }

            this.showAddressActivityForm(addressId, address);
        } catch (error) {
            console.error('Error creating address activity form:', error);
            this.ui.showDialog('Error', `Failed to create activity form: ${error.message}`, 'error');
        }
    }

    showAddressActivityForm(addressId, address) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = 'address-activity-modal';

        const addressName = address.street_address || 'Unknown Address';
        const addressLocation = [address.city, address.province].filter(Boolean).join(', ');

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Add Activity - ${addressName}</h3>
                    <button type="button" class="close-button" data-action="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="address-activity-form">
                        <div class="form-group">
                            <label for="activity-type">Activity Type *</label>
                            <select id="activity-type" name="activity_type" required>
                                <option value="">Select activity type...</option>
                                <option value="Site Visit">Site Visit</option>
                                <option value="Property Check">Property Check</option>
                                <option value="Maintenance">Maintenance</option>
                                <option value="Inspection">Inspection</option>
                                <option value="Issue Reported">Issue Reported</option>
                                <option value="Follow-up">Follow-up</option>
                                <option value="Meeting">Meeting</option>
                                <option value="Service Delivery">Service Delivery</option>
                                <option value="Other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="activity-title">Title</label>
                            <input type="text" id="activity-title" name="title" placeholder="Brief title for this activity">
                        </div>

                        <div class="form-group">
                            <label for="activity-description">Description *</label>
                            <textarea id="activity-description" name="description" rows="4" placeholder="Detailed description of the activity..." required></textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity-date">Activity Date *</label>
                                <input type="date" id="activity-date" name="activity_date" value="${new Date().toISOString().split('T')[0]}" required>
                            </div>
                            <div class="form-group">
                                <label for="activity-time">Activity Time</label>
                                <input type="time" id="activity-time" name="activity_time" value="${new Date().toTimeString().slice(0,5)}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="staff-member">Staff Member</label>
                            <input type="text" id="staff-member" name="staff_member" value="${this.auth.getCurrentUser()?.email || ''}" placeholder="Staff member name">
                        </div>

                        <div class="form-group">
                            <label for="location-notes">Location Notes</label>
                            <textarea id="location-notes" name="location_notes" rows="2" placeholder="Specific location details within or around the address..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="follow-up-required">
                                <input type="checkbox" id="follow-up-required" name="follow_up_required" value="true">
                                Follow-up Required
                            </label>
                        </div>

                        <div class="form-group" id="follow-up-date-group" style="display: none;">
                            <label for="follow-up-date">Follow-up Date</label>
                            <input type="date" id="follow-up-date" name="follow_up_date">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" data-action="close-modal">Cancel</button>
                    <button type="button" class="primary-button" id="save-address-activity">Save Activity</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'flex';

        // Show/hide follow-up date based on checkbox
        const followUpCheckbox = modal.querySelector('#follow-up-required');
        const followUpDateGroup = modal.querySelector('#follow-up-date-group');
        
        followUpCheckbox.addEventListener('change', () => {
            followUpDateGroup.style.display = followUpCheckbox.checked ? 'block' : 'none';
        });

        // Handle form submission
        const saveButton = modal.querySelector('#save-address-activity');
        saveButton.addEventListener('click', async () => {
            await this.saveAddressActivity(addressId, modal);
        });

        // Handle cancel/close buttons
        const cancelButtons = modal.querySelectorAll('[data-action="close-modal"]');
        cancelButtons.forEach(button => {
            button.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });

        // Focus on activity type
        setTimeout(() => {
            modal.querySelector('#activity-type').focus();
        }, 100);
    }

    async saveAddressActivity(addressId, modal) {
        try {
            const form = modal.querySelector('#address-activity-form');
            const formData = new FormData(form);
            
            // Validate required fields
            const activityType = formData.get('activity_type');
            const description = formData.get('description');
            const activityDate = formData.get('activity_date');
            
            if (!activityType || !description || !activityDate) {
                this.ui.showDialog('Error', 'Please fill in all required fields.', 'error');
                return;
            }

            // Prepare activity data
            const activityData = {
                address_id: addressId,
                activity_type: activityType,
                title: formData.get('title') || null,
                description: description,
                activity_date: activityDate,
                activity_time: formData.get('activity_time') || null,
                staff_member: formData.get('staff_member') || null,
                location_notes: formData.get('location_notes') || null,
                follow_up_required: formData.get('follow_up_required') === 'true',
                follow_up_date: formData.get('follow_up_date') || null,
                created_by: this.auth.getCurrentUser()?.email,
                created_at: new Date().toISOString()
            };

            // Save to database using data layer for caching
            const result = await this.data.insert('address_activities', activityData);
            
            if (result) {
                this.ui.showDialog('Success', 'Address activity saved successfully.', 'success');
                
                // Close modal
                document.body.removeChild(modal);
                
                // Refresh the activities list if we're on the address detail page
                const activitiesList = document.getElementById('activities-list');
                if (activitiesList) {
                    // Reload activities for this address
                    if (window.app && window.app.addressManager) {
                        window.app.addressManager.loadAddressActivities(addressId);
                    }
                }
            }

        } catch (error) {
            console.error('Error saving address activity:', error);
            this.ui.showDialog('Error', `Failed to save activity: ${error.message}`, 'error');
        }
    }
}

class SearchAddressesInputCommand extends BaseCommand {
    async execute(args) {
        // This command is handled by the address manager's search functionality
        // The actual search is triggered by input events in the address manager
        console.log('🔍 Address search input triggered');
    }
}

class CancelAddressFormCommand extends BaseCommand {
    async execute(args) {
        await this.app.loadAddressesManagementContent();
    }
}

class EditAddressActivityCommand extends BaseCommand {
    async execute(args) {
        const activityId = args.activityId || args[0];
        const addressId = args.addressId || args[1];
        
        if (!activityId) {
            this.ui.showDialog('Error', 'Activity ID is required for editing.', 'error');
            return;
        }

        try {
            // Get the current activity data
            const activity = await this.data.get('address_activities', activityId);
            if (!activity) {
                this.ui.showDialog('Error', 'Activity not found.', 'error');
                return;
            }

            // Get the address for context
            const address = await this.data.get('addresses', addressId || activity.address_id);
            if (!address) {
                this.ui.showDialog('Error', 'Address not found.', 'error');
                return;
            }

            this.showEditAddressActivityForm(activityId, activity, address);
        } catch (error) {
            console.error('Error loading activity for editing:', error);
            this.ui.showDialog('Error', `Failed to load activity: ${error.message}`, 'error');
        }
    }

    showEditAddressActivityForm(activityId, activity, address) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.id = 'edit-address-activity-modal';

        const addressName = address.street_address || 'Unknown Address';
        const addressLocation = [address.city, address.province].filter(Boolean).join(', ');

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Edit Activity - ${addressName}</h3>
                    <button type="button" class="close-button" data-action="close-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="edit-address-activity-form">
                        <div class="form-group">
                            <label for="activity-type">Activity Type *</label>
                            <select id="activity-type" name="activity_type" required>
                                <option value="">Select activity type...</option>
                                <option value="Site Visit" ${activity.activity_type === 'Site Visit' ? 'selected' : ''}>Site Visit</option>
                                <option value="Property Check" ${activity.activity_type === 'Property Check' ? 'selected' : ''}>Property Check</option>
                                <option value="Maintenance" ${activity.activity_type === 'Maintenance' ? 'selected' : ''}>Maintenance</option>
                                <option value="Inspection" ${activity.activity_type === 'Inspection' ? 'selected' : ''}>Inspection</option>
                                <option value="Issue Reported" ${activity.activity_type === 'Issue Reported' ? 'selected' : ''}>Issue Reported</option>
                                <option value="Follow-up" ${activity.activity_type === 'Follow-up' ? 'selected' : ''}>Follow-up</option>
                                <option value="Meeting" ${activity.activity_type === 'Meeting' ? 'selected' : ''}>Meeting</option>
                                <option value="Service Delivery" ${activity.activity_type === 'Service Delivery' ? 'selected' : ''}>Service Delivery</option>
                                <option value="Other" ${activity.activity_type === 'Other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="activity-title">Title</label>
                            <input type="text" id="activity-title" name="title" value="${activity.title || ''}" placeholder="Brief title for this activity">
                        </div>

                        <div class="form-group">
                            <label for="activity-description">Description *</label>
                            <textarea id="activity-description" name="description" rows="4" placeholder="Detailed description of the activity..." required>${activity.description || ''}</textarea>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="activity-date">Activity Date *</label>
                                <input type="date" id="activity-date" name="activity_date" value="${activity.activity_date || ''}" required>
                            </div>
                            <div class="form-group">
                                <label for="activity-time">Activity Time</label>
                                <input type="time" id="activity-time" name="activity_time" value="${activity.activity_time || ''}">
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="staff-member">Staff Member</label>
                            <input type="text" id="staff-member" name="staff_member" value="${activity.staff_member || ''}" placeholder="Staff member name">
                        </div>

                        <div class="form-group">
                            <label for="location-notes">Location Notes</label>
                            <textarea id="location-notes" name="location_notes" rows="2" placeholder="Specific location details within or around the address...">${activity.location_notes || ''}</textarea>
                        </div>

                        <div class="form-group">
                            <label for="follow-up-required">
                                <input type="checkbox" id="follow-up-required" name="follow_up_required" value="true" ${activity.follow_up_required ? 'checked' : ''}>
                                Follow-up Required
                            </label>
                        </div>

                        <div class="form-group" id="follow-up-date-group" style="display: ${activity.follow_up_required ? 'block' : 'none'};">
                            <label for="follow-up-date">Follow-up Date</label>
                            <input type="date" id="follow-up-date" name="follow_up_date" value="${activity.follow_up_date || ''}">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="secondary-button" data-action="close-modal">Cancel</button>
                    <button type="button" class="primary-button" id="update-address-activity">Update Activity</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        modal.style.display = 'flex';

        // Show/hide follow-up date based on checkbox
        const followUpCheckbox = modal.querySelector('#follow-up-required');
        const followUpDateGroup = modal.querySelector('#follow-up-date-group');
        
        followUpCheckbox.addEventListener('change', () => {
            followUpDateGroup.style.display = followUpCheckbox.checked ? 'block' : 'none';
        });

        // Handle save button
        const updateButton = modal.querySelector('#update-address-activity');
        updateButton.addEventListener('click', async () => {
            await this.updateAddressActivity(activityId, address.id, modal);
        });

        // Handle modal close
        const closeButtons = modal.querySelectorAll('[data-action="close-modal"]');
        closeButtons.forEach(button => {
            button.addEventListener('click', () => {
                document.body.removeChild(modal);
            });
        });
    }

    async updateAddressActivity(activityId, addressId, modal) {
        try {
            const form = modal.querySelector('#edit-address-activity-form');
            const formData = new FormData(form);
            
            // Validate required fields
            const activityType = formData.get('activity_type');
            const description = formData.get('description');
            const activityDate = formData.get('activity_date');
            
            if (!activityType || !description || !activityDate) {
                this.ui.showDialog('Error', 'Please fill in all required fields.', 'error');
                return;
            }

            // Prepare update data
            const updateData = {
                activity_type: activityType,
                title: formData.get('title') || null,
                description: description,
                activity_date: activityDate,
                activity_time: formData.get('activity_time') || null,
                staff_member: formData.get('staff_member') || null,
                location_notes: formData.get('location_notes') || null,
                follow_up_required: formData.get('follow_up_required') === 'true',
                follow_up_date: formData.get('follow_up_date') || null,
                updated_by: this.auth.getCurrentUser()?.email,
                updated_at: new Date().toISOString()
            };

            // Update the activity using data layer for caching
            await this.data.update('address_activities', activityId, updateData);
            
            this.ui.showDialog('Success', 'Address activity updated successfully.', 'success');
            
            // Close modal
            document.body.removeChild(modal);
            
            // Refresh the activities list if we're on the address detail page
            if (window.app && window.app.addressManager) {
                window.app.addressManager.loadAddressActivities(addressId);
            }
            
        } catch (error) {
            console.error('Error updating address activity:', error);
            this.ui.showDialog('Error', `Failed to update activity: ${error.message}`, 'error');
        }
    }
}

class DeleteAddressActivityCommand extends BaseCommand {
    async execute(args) {
        const activityId = args.activityId || args[0];
        const addressId = args.addressId || args[1];
        
        if (!activityId) {
            this.ui.showDialog('Error', 'Activity ID is required for deletion.', 'error');
            return;
        }

        try {
            // Get the activity to show confirmation details
            const activity = await this.data.get('address_activities', activityId);
            if (!activity) {
                this.ui.showDialog('Error', 'Activity not found.', 'error');
                return;
            }

            // Show confirmation dialog
            const confirmed = await this.ui.showConfirmDialog(
                'Delete Activity',
                `Are you sure you want to delete the activity "${activity.title || activity.activity_type}"?`,
                'warning'
            );

            if (confirmed) {
                // Delete the activity using data layer for caching
                await this.data.delete('address_activities', activityId);
                
                this.ui.showDialog('Success', 'Address activity deleted successfully.', 'success');
                
                // Refresh the activities list if we're on the address detail page
                if (window.app && window.app.addressManager) {
                    window.app.addressManager.loadAddressActivities(addressId || activity.address_id);
                }
            }
            
        } catch (error) {
            console.error('Error deleting address activity:', error);
            this.ui.showDialog('Error', `Failed to delete activity: ${error.message}`, 'error');
        }
    }
}

class AddOrganizationCommand extends BaseCommand {
    async execute(args) {
        const fields = this.data.schema.generateFormFields('organizations');
        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Organization', fields, async (formData) => {
                try {
                    const dbData = this.data.schema.convertFormToDatabase('organizations', formData);
                    const organizationData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };
                    const result = await this.data.insert('organizations', organizationData);
                    this.ui.showDialog(
                        'Organization Added',
                        `Organization ${formData.name} has been created.`,
                        'success'
                    );
                    resolve(result);
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add organization: ${error.message}`, 'error');
                    resolve(null);
                    return false;
                }
            });
        });
    }
}

class SearchOrganizationsCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                await this.app.showOrganizationSearchModal();
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to open organization search: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class ListOrganizationsCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                await this.app.showOrganizationListView();
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load organization list: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class ViewOrganizationDetailCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const organizationId = args.organizationId || args[0];
                if (!organizationId) {
                    this.ui.showDialog('Error', 'Organization ID is required.', 'error');
                    resolve(null);
                    return;
                }
                
                const organization = await this.data.get('organizations', organizationId);
                if (!organization) {
                    this.ui.showDialog('Error', 'Organization not found.', 'error');
                    resolve(null);
                    return;
                }
                
                await this.app.showOrganizationDetailView(organization);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load organization details: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class EditOrganizationCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const organizationId = args.organizationId || args[0];
                if (!organizationId) {
                    this.ui.showDialog('Error', 'Organization ID is required.', 'error');
                    resolve(null);
                    return;
                }
                
                const organization = await this.data.get('organizations', organizationId);
                if (!organization) {
                    this.ui.showDialog('Error', 'Organization not found.', 'error');
                    resolve(null);
                    return;
                }
                
                await this.app.showOrganizationForm(organization, true);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load organization form: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class AddOrganizationFormCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                await this.app.showOrganizationForm();
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to open organization form: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class BackToOrganizationListCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                await this.app.showOrganizationListView();
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load organization list: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class BackToOrganizationDetailCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const organizationId = args.organizationId || args[0];
                if (!organizationId) {
                    this.ui.showDialog('Error', 'Organization ID is required.', 'error');
                    resolve(null);
                    return;
                }
                
                const organization = await this.data.get('organizations', organizationId);
                if (!organization) {
                    this.ui.showDialog('Error', 'Organization not found.', 'error');
                    resolve(null);
                    return;
                }
                
                await this.app.showOrganizationDetailView(organization);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load organization detail: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class SaveOrganizationCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const form = document.getElementById('organization-form');
                if (!form) {
                    this.ui.showDialog('Error', 'Organization form not found.', 'error');
                    resolve(null);
                    return;
                }
                
                const organizationId = args.organizationId;
                const isEdit = !!organizationId;
                let existingOrganization = null;
                
                if (isEdit) {
                    existingOrganization = await this.data.get('organizations', organizationId);
                    if (!existingOrganization) {
                        this.ui.showDialog('Error', 'Organization not found.', 'error');
                        resolve(null);
                        return;
                    }
                }
                
                await this.app.saveOrganization(form, existingOrganization, isEdit);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to save organization: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}


class AddPlateCommand extends BaseCommand {
    async execute(args) {
        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('license_plates');

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add License Plate Record', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('license_plates', formData);

                    const plateData = {
                        ...dbData,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    const result = await this.data.insert('license_plates', plateData);

                    this.ui.showDialog(
                        'Vehicle Added',
                        `Vehicle record for ${formData.plate_number} has been created.`,
                        'success'
                    );

                    resolve(result);
                    return true; // Indicate success for form navigation
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add vehicle: ${error.message}`, 'error');
                    resolve(null);
                    return false; // Indicate failure to stay on form
                }
            });
        });
    }
}

class AddVehicleCommand extends BaseCommand {
    async execute(args) {
        // Define form fields for license plates
        const fields = [
            { name: 'plate_number', label: 'License Plate Number', type: 'text', required: true },
            { 
                name: 'province', 
                label: 'Province/State', 
                type: 'select',
                defaultValue: 'ON',
                options: [
                    { value: '', label: 'CANADA', disabled: true, isHeader: true },
                    { value: 'AB', label: 'Alberta' },
                    { value: 'BC', label: 'British Columbia' },
                    { value: 'MB', label: 'Manitoba' },
                    { value: 'NB', label: 'New Brunswick' },
                    { value: 'NL', label: 'Newfoundland and Labrador' },
                    { value: 'NS', label: 'Nova Scotia' },
                    { value: 'ON', label: 'Ontario', selected: true },
                    { value: 'PE', label: 'Prince Edward Island' },
                    { value: 'QC', label: 'Quebec' },
                    { value: 'SK', label: 'Saskatchewan' },
                    { value: 'NT', label: 'Northwest Territories' },
                    { value: 'NU', label: 'Nunavut' },
                    { value: 'YT', label: 'Yukon' },
                    { value: '', label: 'UNITED STATES', disabled: true, isHeader: true },
                    { value: 'AL', label: 'Alabama' },
                    { value: 'AK', label: 'Alaska' },
                    { value: 'AZ', label: 'Arizona' },
                    { value: 'AR', label: 'Arkansas' },
                    { value: 'CA', label: 'California' },
                    { value: 'CO', label: 'Colorado' },
                    { value: 'CT', label: 'Connecticut' },
                    { value: 'DE', label: 'Delaware' },
                    { value: 'FL', label: 'Florida' },
                    { value: 'GA', label: 'Georgia' },
                    { value: 'HI', label: 'Hawaii' },
                    { value: 'ID', label: 'Idaho' },
                    { value: 'IL', label: 'Illinois' },
                    { value: 'IN', label: 'Indiana' },
                    { value: 'IA', label: 'Iowa' },
                    { value: 'KS', label: 'Kansas' },
                    { value: 'KY', label: 'Kentucky' },
                    { value: 'LA', label: 'Louisiana' },
                    { value: 'ME', label: 'Maine' },
                    { value: 'MD', label: 'Maryland' },
                    { value: 'MA', label: 'Massachusetts' },
                    { value: 'MI', label: 'Michigan' },
                    { value: 'MN', label: 'Minnesota' },
                    { value: 'MS', label: 'Mississippi' },
                    { value: 'MO', label: 'Missouri' },
                    { value: 'MT', label: 'Montana' },
                    { value: 'NE', label: 'Nebraska' },
                    { value: 'NV', label: 'Nevada' },
                    { value: 'NH', label: 'New Hampshire' },
                    { value: 'NJ', label: 'New Jersey' },
                    { value: 'NM', label: 'New Mexico' },
                    { value: 'NY', label: 'New York' },
                    { value: 'NC', label: 'North Carolina' },
                    { value: 'ND', label: 'North Dakota' },
                    { value: 'OH', label: 'Ohio' },
                    { value: 'OK', label: 'Oklahoma' },
                    { value: 'OR', label: 'Oregon' },
                    { value: 'PA', label: 'Pennsylvania' },
                    { value: 'RI', label: 'Rhode Island' },
                    { value: 'SC', label: 'South Carolina' },
                    { value: 'SD', label: 'South Dakota' },
                    { value: 'TN', label: 'Tennessee' },
                    { value: 'TX', label: 'Texas' },
                    { value: 'UT', label: 'Utah' },
                    { value: 'VT', label: 'Vermont' },
                    { value: 'VA', label: 'Virginia' },
                    { value: 'WA', label: 'Washington' },
                    { value: 'WV', label: 'West Virginia' },
                    { value: 'WI', label: 'Wisconsin' },
                    { value: 'WY', label: 'Wyoming' },
                    { value: 'DC', label: 'District of Columbia' }
                ]
            },
            { name: 'vehicle_make', label: 'Vehicle Make', type: 'text' },
            { name: 'vehicle_model', label: 'Vehicle Model', type: 'text' },
            { name: 'vehicle_year', label: 'Vehicle Year', type: 'number' },
            { name: 'vehicle_color', label: 'Vehicle Color', type: 'text' },
            { 
                name: 'vehicle_type', 
                label: 'Vehicle Type', 
                type: 'select',
                options: [
                    { value: '', label: '-- Select Vehicle Type --' },
                    { value: 'car', label: 'Car' },
                    { value: 'pickup_truck', label: 'Pickup Truck' },
                    { value: 'van', label: 'Van' },
                    { value: 'suv', label: 'SUV' },
                    { value: 'commercial_vehicle', label: 'Commercial Vehicle' },
                    { value: 'motorcycle', label: 'Motorcycle' },
                    { value: 'other', label: 'Other' }
                ]
            },
            { name: 'owner_name', label: 'Owner Name', type: 'text' },
            { name: 'notes', label: 'Notes', type: 'textarea' }
        ];

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Vehicle Record', fields, async (formData) => {
                try {
                    // Map form data to database schema - don't include id (auto-generated by Supabase)
                    const vehicleData = {
                        plate_number: formData.plate_number,
                        province: formData.province || null,
                        vehicle_make: formData.vehicle_make || null,
                        vehicle_model: formData.vehicle_model || null,
                        vehicle_year: formData.vehicle_year ? parseInt(formData.vehicle_year) : null,
                        vehicle_color: formData.vehicle_color || null,
                        vehicle_type: formData.vehicle_type || null,
                        owner_name: formData.owner_name || null,
                        notes: formData.notes || null,
                        created_by: this.auth.getCurrentUser()?.email
                    };

                    const result = await this.data.insert('license_plates', vehicleData);

                    this.ui.showDialog(
                        'Vehicle Added',
                        `Vehicle record for ${formData.plate_number} has been created.`,
                        'success'
                    );

                    resolve(result);
                    return true; // Indicate success for form navigation
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add vehicle: ${error.message}`, 'error');
                    resolve(null);
                    return false; // Indicate failure to stay on form
                }
            });
        });
    }
}

class SearchVehiclesCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showVehicleSearchInterface(resolve);
        });
    }

    showVehicleSearchInterface(resolve) {
        // Create search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Search Vehicles</h3>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="vehicle-search-query">Search Vehicles:</label>
                            <input type="text" id="vehicle-search-query" name="vehicle-search-query" placeholder="Enter license plate, make, model, VIN...">
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn-primary" id="search-vehicles-btn">Search</button>
                            <button type="button" class="btn-secondary" id="cancel-vehicle-search">Cancel</button>
                        </div>
                    </div>
                    <div class="search-results" id="vehicle-search-results"></div>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        // Handle search
        const searchBtn = searchModal.querySelector('#search-vehicles-btn');
        const searchInput = searchModal.querySelector('#vehicle-search-query');
        const resultsDiv = searchModal.querySelector('#vehicle-search-results');

        const performSearch = async () => {
            const query = searchInput.value.trim();
            if (!query) {
                this.ui.showDialog('Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                resultsDiv.innerHTML = '<div class="loading">Searching vehicles...</div>';
                
                // Search vehicles table using searchFields format
                const vehicles = await this.data.search('license_plates', {
                    searchFields: ['plate_number', 'vehicle_make', 'vehicle_model', 'owner_name', 'province'],
                    searchQuery: query
                });

                if (vehicles.length === 0) {
                    resultsDiv.innerHTML = '<div class="no-results">No vehicles found matching your search.</div>';
                    return;
                }

                // Display results
                let resultsHTML = '<div class="search-results-list"><h4>Search Results:</h4>';
                vehicles.forEach(vehicle => {
                    resultsHTML += `
                        <div class="search-result-item vehicle-result" data-vehicle-id="${vehicle.id}">
                            <div class="result-header">
                                <strong>🚗 ${vehicle.plate_number || 'Unknown Plate'}</strong>
                                <span class="result-date">${new Date(vehicle.created_at).toLocaleDateString()}</span>
                            </div>
                            <div class="result-details">
                                <p><strong>Make/Model:</strong> ${vehicle.make || 'Unknown'} ${vehicle.model || ''}</p>
                                <p><strong>Year:</strong> ${vehicle.year || 'Unknown'}</p>
                                <p><strong>Color:</strong> ${vehicle.color || 'Unknown'}</p>
                                <p><strong>Owner:</strong> ${vehicle.owner_name || 'Unknown'}</p>
                                ${vehicle.vin ? `<p><strong>VIN:</strong> ${vehicle.vin}</p>` : ''}
                                ${vehicle.notes ? `<p><strong>Notes:</strong> ${vehicle.notes}</p>` : ''}
                            </div>
                        </div>
                    `;
                });
                resultsHTML += '</div>';
                resultsDiv.innerHTML = resultsHTML;

            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error searching vehicles: ${error.message}</div>`;
            }
        };

        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Handle cancel
        const cancelBtn = searchModal.querySelector('#cancel-vehicle-search');
        cancelBtn.addEventListener('click', () => {
            document.body.removeChild(searchModal);
            resolve(null);
        });

        // Close on overlay click
        searchModal.addEventListener('click', (e) => {
            if (e.target === searchModal) {
                document.body.removeChild(searchModal);
                resolve(null);
            }
        });

        // Focus search input
        searchInput.focus();
    }
}

class ListVehiclesCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                // Fetch all vehicles from the database
                const vehicles = await this.data.search('license_plates', {});

                // Use the app's showVehicleListView method
                await this.app.showVehicleListView(vehicles);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load vehicles: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}



class EditVehicleCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const vehicleId = args.vehicleId || args[0];
                if (!vehicleId) {
                    this.ui.showDialog('Error', 'No vehicle ID provided', 'error');
                    resolve(null);
                    return;
                }

                // Use the vehicle management module to show edit form
                await this.app.vehicleManagement.showEditVehicleForm(vehicleId);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to edit vehicle: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class BackToVehicleListCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                // Fetch all vehicles and show the management view
                const vehicles = await this.data.search('license_plates', {});
                await this.app.showVehicleManagementView(vehicles);
                resolve(true);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to load vehicle list: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }
}

class SearchVehiclesInputCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                const searchTerm = args.value || '';

                if (!searchTerm.trim()) {
                    // If empty search, show all vehicles
                    const vehicles = await this.data.search('license_plates', {});
                    await this.app.updateVehicleList(vehicles);
                } else {
                    // Search vehicles using the correct format
                    const vehicles = await this.data.search('license_plates', {
                        searchFields: ['plate_number', 'vehicle_make', 'vehicle_model', 'owner_name', 'province'],
                        searchQuery: searchTerm
                    });
                    await this.app.updateVehicleList(vehicles);
                }

                resolve(true);
            } catch (error) {
                console.error('Error searching vehicles:', error);
                resolve(null);
            }
        });
    }
}

class GenerateReportCommand extends BaseCommand {
    async execute(args) {
        this.ui.showDialog('Generate Report', 'Report generation functionality coming soon!', 'info');
    }
}

class ExportDataCommand extends BaseCommand {
    async execute(args) {
        this.ui.showDialog('Export Data', 'Data export functionality coming soon!', 'info');
    }
}

class SyncDataCommand extends BaseCommand {
    async execute(args) {
        try {
            await this.data.syncPendingData();
            this.ui.showDialog('Sync Complete', 'Data synchronization completed successfully.', 'success');
        } catch (error) {
            this.ui.showDialog('Sync Error', `Failed to sync data: ${error.message}`, 'error');
        }
    }
}

class SettingsCommand extends BaseCommand {
    async execute(args) {
        // Delegate to SystemSettingsManager
        return await this.app.systemSettingsManager.showSettingsMenu();
    }

}

class AboutCommand extends BaseCommand {
    async execute(args) {
        const user = this.auth.getCurrentUser();
        const stats = this.data.getStats();

        // Get current version dynamically
        const currentVersion = await window.electronAPI.invoke('app-version');

        const aboutText = `
S.T.E.V.I Retro v${currentVersion}
Supportive Technology to Enable Vulnerable Individuals - Retro Interface

Current User: ${user?.email || 'Not logged in'}
Cache Size: ${stats.cacheSize} items
Online Status: ${stats.isOnline ? 'Connected' : 'Offline'}
Pending Sync: ${stats.pendingSync} operations

© 2024 I.H.A.R.C. All rights reserved.
        `.trim();

        this.ui.showDialog('About S.T.E.V.I Retro', aboutText, 'info');
    }
}

class EmergencyContactsCommand extends BaseCommand {
    async execute(args) {
        const emergencyContacts = `
🚑 EMERGENCY CONTACTS

Emergency Services: 911

Crisis Lines:
• Crisis & Suicide Line: 1-************
• Mental Health Helpline: 1-************
• Addiction Helpline: 1-************

Shelters:
• Emergency Shelter: (*************
• Women's Shelter: (*************
• Youth Shelter: (*************

Medical:
• Mobile Medical Unit: (*************
• Detox Center: (*************
• Harm Reduction: (*************

Support Services:
• Food Bank: (*************
• Clothing Depot: (*************
• Transportation: (*************

IHARC Dispatch: (*************
        `.trim();

        this.ui.showDialog('Emergency Contacts', emergencyContacts, 'info');
    }
}

class UserProfileCommand extends BaseCommand {
    async execute(args) {
        this.showUserProfile();
    }

    showUserProfile() {
        const currentUser = this.auth.getCurrentUser();

        if (!currentUser) {
            this.ui.showDialog('Error', 'No user session found', 'error');
            return;
        }

        const profileModal = document.createElement('div');
        profileModal.className = 'modal-overlay';

        profileModal.innerHTML = `
            <div class="modal-dialog user-profile-modal">
                <div class="modal-header">
                    <h3>👤 User Profile</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="profile-container">
                        <!-- Profile Information -->
                        <div class="profile-info-section">
                            <h4>Profile Information</h4>
                            <div class="profile-details">
                                <div class="profile-field">
                                    <label>Email:</label>
                                    <span class="field-value">${currentUser.email || 'Not available'}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Name:</label>
                                    <span class="field-value" id="user-name">${currentUser.name || currentUser.user_metadata?.name || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="name">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>IHARC Staff ID:</label>
                                    <span class="field-value" id="user-staff-id">${currentUser.iharc_staff_id || 'Not assigned'}</span>
                                    ${currentUser.role === 'iharc_admin' ? '<button class="edit-field-btn" data-field="staff_id">Edit</button>' : '<span class="field-note">(Contact admin to update)</span>'}
                                </div>
                                <div class="profile-field">
                                    <label>Ranger ID:</label>
                                    <span class="field-value" id="user-ranger-id">${currentUser.ranger_id || 'Not assigned'}</span>
                                    ${currentUser.role === 'iharc_admin' ? '<button class="edit-field-btn" data-field="ranger_id">Edit</button>' : '<span class="field-note">(Contact admin to update)</span>'}
                                </div>
                                <div class="profile-field">
                                    <label>Phone:</label>
                                    <span class="field-value" id="user-phone">${currentUser.phone || currentUser.user_metadata?.phone || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="phone">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>Department:</label>
                                    <span class="field-value" id="user-department">${currentUser.department || currentUser.user_metadata?.department || 'Not set'}</span>
                                    <button class="edit-field-btn" data-field="department">Edit</button>
                                </div>
                                <div class="profile-field">
                                    <label>Role:</label>
                                    <span class="field-value">${currentUser.role || currentUser.user_metadata?.role || 'Outreach Staff'}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Last Login:</label>
                                    <span class="field-value">${this.formatDate(currentUser.last_sign_in_at)}</span>
                                </div>
                                <div class="profile-field">
                                    <label>Account Created:</label>
                                    <span class="field-value">${this.formatDate(currentUser.created_at)}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Section -->
                        <div class="security-section">
                            <h4>Security</h4>
                            <div class="security-actions">
                                <button class="action-button" id="change-password-btn">
                                    <span class="action-icon">🔒</span>
                                    <span class="action-text">Change Password</span>
                                </button>
                                <button class="action-button" id="reset-password-btn">
                                    <span class="action-icon">📧</span>
                                    <span class="action-text">Reset Password via Email</span>
                                </button>
                            </div>
                        </div>

                        <!-- Session Information -->
                        <div class="session-section">
                            <h4>Session Information</h4>
                            <div class="session-details">
                                <div class="session-field">
                                    <label>Session Status:</label>
                                    <span class="field-value status-active">Active</span>
                                </div>
                                <div class="session-field">
                                    <label>Login Method:</label>
                                    <span class="field-value">${currentUser.app_metadata?.provider || 'Email'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(profileModal);

        // Set up event handlers
        this.setupProfileHandlers(profileModal);
    }

    setupProfileHandlers(modal) {
        // Edit field buttons
        modal.querySelectorAll('.edit-field-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const fieldName = e.target.dataset.field;
                this.editProfileField(fieldName);
            });
        });

        // Change password button
        const changePasswordBtn = modal.querySelector('#change-password-btn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', () => {
                this.showChangePasswordForm();
            });
        }

        // Reset password button
        const resetPasswordBtn = modal.querySelector('#reset-password-btn');
        if (resetPasswordBtn) {
            resetPasswordBtn.addEventListener('click', () => {
                this.showResetPasswordForm();
            });
        }
    }

    editProfileField(fieldName) {
        const currentUser = this.auth.getCurrentUser();

        // Prevent staff from editing their staff ID
        if (fieldName === 'staff_id' && currentUser.role !== 'iharc_admin') {
            this.ui.showDialog('Access Denied', 'Only administrators can edit staff ID numbers. Please contact an admin to update this field.', 'error');
            return;
        }

        const currentValue = currentUser[fieldName] || currentUser.user_metadata?.[fieldName] || '';

        const fieldLabels = {
            name: 'Full Name',
            phone: 'Phone Number',
            department: 'Department',
            staff_id: 'IHARC Staff ID',
            ranger_id: 'Ranger ID'
        };

        const label = fieldLabels[fieldName] || fieldName;

        this.ui.showForm(
            `Edit ${label}`,
            [{
                name: fieldName,
                label: label,
                type: 'text',
                required: fieldName === 'name',
                value: currentValue
            }],
            async (formData) => {
                try {
                    const result = await this.auth.updateProfile({
                        [fieldName]: formData[fieldName]
                    });

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');

                        // Update the display
                        const fieldElement = document.getElementById(`user-${fieldName}`);
                        if (fieldElement) {
                            fieldElement.textContent = formData[fieldName] || 'Not set';
                        }

                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update ${label.toLowerCase()}: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    showChangePasswordForm() {
        this.ui.showForm(
            'Change Password',
            [
                {
                    name: 'currentPassword',
                    label: 'Current Password',
                    type: 'password',
                    required: true
                },
                {
                    name: 'newPassword',
                    label: 'New Password',
                    type: 'password',
                    required: true
                },
                {
                    name: 'confirmPassword',
                    label: 'Confirm New Password',
                    type: 'password',
                    required: true
                }
            ],
            async (formData) => {
                try {
                    // Validate passwords match
                    if (formData.newPassword !== formData.confirmPassword) {
                        this.ui.showDialog('Error', 'New passwords do not match', 'error');
                        return false;
                    }

                    // Validate password strength
                    if (formData.newPassword.length < 8) {
                        this.ui.showDialog('Error', 'Password must be at least 8 characters long', 'error');
                        return false;
                    }

                    const result = await this.auth.changePassword(formData.newPassword);

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');
                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to change password: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    showResetPasswordForm() {
        const currentUser = this.auth.getCurrentUser();

        this.ui.showForm(
            'Reset Password via Email',
            [{
                name: 'email',
                label: 'Email Address',
                type: 'email',
                required: true,
                value: currentUser.email || ''
            }],
            async (formData) => {
                try {
                    const result = await this.auth.resetPassword(formData.email);

                    if (result.success) {
                        this.ui.showDialog('Success', result.message, 'success');
                        return true;
                    } else {
                        this.ui.showDialog('Error', result.message, 'error');
                        return false;
                    }
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to send reset email: ${error.message}`, 'error');
                    return false;
                }
            }
        );
    }

    formatDate(dateString) {
        if (!dateString) return 'Not available';
        try {
            return new Date(dateString).toLocaleString();
        } catch {
            return 'Invalid date';
        }
    }
}

// Check Updates Command
class CheckUpdatesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking for updates...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            const updateInfo = await window.updateUIManager.checkForUpdates(true);

            if (updateInfo.available) {
                this.ui.setStatus(`Update available: v${updateInfo.latestVersion}`, 'success');
            } else {
                this.ui.setStatus('No updates available', 'info');
            }

            return updateInfo;
        } catch (error) {
            this.ui.setStatus('Failed to check for updates', 'error');
            throw error;
        }
    }
}

// Update Command (Download and Install)
class UpdateCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking for updates...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            const updateInfo = await window.updateUIManager.checkForUpdates();

            if (updateInfo.available) {
                this.ui.setStatus(`Starting update to v${updateInfo.latestVersion}...`, 'info');
                await window.updateUIManager.startUpdateDownload(updateInfo);
            } else {
                this.ui.setStatus('No updates available', 'info');
                window.updateUIManager.showNoUpdateDialog();
            }

            return updateInfo;
        } catch (error) {
            this.ui.setStatus('Update failed', 'error');
            throw error;
        }
    }
}

// Release Notes Command
class ReleaseNotesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Loading release notes...', 'info');

            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            // Show release notes for current version
            await this.showReleaseNotesDialog();

            this.ui.setStatus('Ready', 'info');
        } catch (error) {
            this.ui.setStatus('Failed to load release notes', 'error');
            throw error;
        }
    }

    async showReleaseNotesDialog() {
        try {
            // Get current version release notes
            const currentVersion = await window.electronAPI.invoke('app-version');

            // Try to get release notes from GitHub
            let releaseNotes = 'Release notes not available.';
            let releaseDate = 'Unknown';

            try {
                const updateInfo = await window.electronAPI.invoke('check-for-updates');
                if (updateInfo && updateInfo.releaseNotes) {
                    releaseNotes = updateInfo.releaseNotes;
                    releaseDate = new Date(updateInfo.publishedAt).toLocaleDateString();
                }
            } catch (error) {
                console.log('Could not fetch release notes from GitHub:', error.message);
                // Use fallback release notes
                releaseNotes = this.getFallbackReleaseNotes(currentVersion);
            }

            const dialog = document.createElement('div');
            dialog.className = 'modal-overlay release-notes-dialog';

            dialog.innerHTML = `
                <div class="modal-dialog" style="max-width: 700px;">
                    <div class="modal-header">
                        <h3>📋 Release Notes - Version ${currentVersion}</h3>
                    </div>
                    <div class="modal-body">
                        <div class="release-info">
                            <p><strong>Version:</strong> ${currentVersion}</p>
                            <p><strong>Release Date:</strong> ${releaseDate}</p>
                        </div>
                        <div class="release-notes">
                            <h4>What's New:</h4>
                            <div class="release-notes-content">${this.formatReleaseNotes(releaseNotes)}</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                        <button class="secondary-button" id="check-updates-btn">Check for Updates</button>
                    </div>
                </div>
            `;

            document.body.appendChild(dialog);

            // Set up check updates button
            dialog.querySelector('#check-updates-btn').onclick = async () => {
                dialog.remove();
                try {
                    await this.manager.executeCommand('check-updates');
                } catch (error) {
                    console.error('Error checking for updates:', error);
                }
            };

        } catch (error) {
            console.error('Error showing release notes:', error);
            this.ui.showDialog('Error', 'Failed to load release notes', 'error');
        }
    }

    getFallbackReleaseNotes(version) {
        // Fallback release notes for when GitHub is not accessible
        return `
            <strong>S.T.E.V.I Retro v${version}</strong><br><br>

            <strong>🚀 Features:</strong><br>
            • Professional incident reporting system<br>
            • Comprehensive record management<br>
            • Offline-first operation with automatic sync<br>
            • Secure authentication and role-based access<br>
            • Retro terminal interface with modern functionality<br><br>

            <strong>🔧 System:</strong><br>
            • Built with Electron for cross-platform compatibility<br>
            • Supabase backend integration<br>
            • Real-time data synchronization<br>
            • Secure local data caching<br><br>

            <strong>📱 Interface:</strong><br>
            • Authentic retro DOS styling<br>
            • Tabbed navigation system<br>
            • Modal dialogs and forms<br>
            • Network status monitoring<br><br>

            For the latest updates and detailed release notes, please check the GitHub repository.
        `;
    }

    formatReleaseNotes(notes) {
        if (!notes) return 'No release notes available.';

        // Convert markdown-style formatting to HTML
        return notes
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/#{1,6}\s*(.*?)$/gm, '<h4>$1</h4>')
            .replace(/^\s*[-*+]\s+(.*)$/gm, '• $1');
    }
}

// Add Activity Command
class AddActivityCommand extends BaseCommand {
    async execute(args) {
        this.showActivityTypeSelection();
    }

    showActivityTypeSelection() {
        // Create activity type selection modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Add Activity - Select Type</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p>What type of activity would you like to add?</p>
                    <div class="activity-type-selection">
                        <div class="menu-grid">
                            <div class="menu-item" data-activity-type="people">
                                <div class="menu-icon">👤</div>
                                <div class="menu-title">Person Activity</div>
                                <div class="menu-desc">Add activity for a person record</div>
                            </div>
                            <div class="menu-item" data-activity-type="addresses">
                                <div class="menu-icon">🏠</div>
                                <div class="menu-title">Address Activity</div>
                                <div class="menu-desc">Add activity for an address record</div>
                            </div>
                            <div class="menu-item" data-activity-type="license_plates">
                                <div class="menu-icon">🚗</div>
                                <div class="menu-title">Vehicle Activity</div>
                                <div class="menu-desc">Add activity for a vehicle record</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle activity type selection
        modal.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const activityType = menuItem.dataset.activityType;
                modal.remove();
                this.showRecordSearch(activityType);
            }
        });
    }

    showRecordSearch(recordType) {
        // Create record search modal
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        const recordTypeNames = {
            people: 'People',
            addresses: 'Addresses',
            license_plates: 'License Plates'
        };

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Select ${recordTypeNames[recordType]} Record</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="search-query">Search For:</label>
                            <input type="text" id="search-query" name="search-query" placeholder="Enter search term...">
                        </div>
                        <button type="button" id="search-submit" class="primary-button">Search</button>
                    </div>
                    <div class="search-results" id="search-results" style="display: none;">
                        <h4>Search Results:</h4>
                        <div class="results-container" id="results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        const searchQuery = searchModal.querySelector('#search-query');
        const searchSubmit = searchModal.querySelector('#search-submit');
        const searchResults = searchModal.querySelector('#search-results');
        const resultsContainer = searchModal.querySelector('#results-container');

        // Handle search submission
        const performSearch = async () => {
            const query = searchQuery.value.trim();

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.performRecordSearch(recordType, query);
                this.displaySearchResults(results, resultsContainer, recordType);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        searchSubmit.addEventListener('click', performSearch);
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Focus search input
        searchQuery.focus();
    }

    async performRecordSearch(recordType, query) {
        try {
            // Get all records and filter client-side for comprehensive search
            const allRecords = await this.data.search(recordType, {});

            if (!allRecords || allRecords.length === 0) {
                return [];
            }

            const lowerQuery = query.toLowerCase();

            return allRecords.filter(record => {
                // Search across all string fields
                return Object.values(record).some(value => {
                    if (typeof value === 'string') {
                        return value.toLowerCase().includes(lowerQuery);
                    }
                    return false;
                });
            });
        } catch (error) {
            console.error(`Error searching ${recordType}:`, error);
            return [];
        }
    }

    displaySearchResults(results, container, recordType) {
        container.innerHTML = '';

        if (results.length === 0) {
            if (recordType === 'people') {
                container.innerHTML = `
                    <div class="no-results">
                        <p>No people records found matching your search.</p>
                        <div class="quick-add-section">
                            <p><strong>Would you like to create a new person record?</strong></p>
                            <button class="primary-button create-new-person-btn">
                                <span class="action-icon">👤</span>
                                Create New Person
                            </button>
                        </div>
                    </div>
                `;
                
                // Add click handler for create new person button
                const createBtn = container.querySelector('.create-new-person-btn');
                createBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.handleCreateNewPerson();
                });
            } else {
                container.innerHTML = '<div class="no-results">No records found matching your search.</div>';
            }
            return;
        }

        results.forEach(record => {
            const recordDiv = document.createElement('div');
            recordDiv.className = 'record-item selectable';
            recordDiv.innerHTML = `
                ${this.formatRecord(recordType, record)}
                <button class="select-record-btn primary-button" data-record-id="${record.id}">Select</button>
            `;

            // Add click handler for select button
            const selectBtn = recordDiv.querySelector('.select-record-btn');
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.handleRecordSelection(record, recordType);
            });

            container.appendChild(recordDiv);
        });
    }

    formatRecord(table, record) {
        switch (table) {
            case 'people':
                return `
                    <div class="record-summary">
                        <strong>${record.first_name || ''} ${record.last_name || ''}</strong>
                        ${record.date_of_birth ? `<br>DOB: ${record.date_of_birth}` : ''}
                        ${record.phone ? `<br>Phone: ${record.phone}` : ''}
                        ${record.email ? `<br>Email: ${record.email}` : ''}
                    </div>
                `;
            case 'addresses':
                return `
                    <div class="record-summary">
                        <strong>${record.street_address || 'Unknown Address'}</strong>
                        <br>${record.city || ''}, ${record.province || ''}
                        ${record.postal_code ? ` ${record.postal_code}` : ''}
                        ${record.country ? `<br>Country: ${record.country}` : ''}
                    </div>
                `;
            case 'license_plates':
                return `
                    <div class="record-summary">
                        <strong>${record.plate_number || 'Unknown Plate'}</strong>
                        <br>Province: ${record.province || 'Unknown'}
                        ${record.vehicle_make ? `<br>Vehicle: ${record.vehicle_year || ''} ${record.vehicle_make} ${record.vehicle_model || ''}`.trim() : ''}
                        ${record.vehicle_color ? `<br>Color: ${record.vehicle_color}` : ''}
                    </div>
                `;
            default:
                return `<div class="record-summary">Record ID: ${record.id}</div>`;
        }
    }

    handleRecordSelection(record, recordType) {
        // Close the search modal
        document.querySelector('.modal-overlay').remove();

        // Show the activity form for the selected record
        this.showAddActivityForm(recordType, record);
    }

    // Reuse the existing showAddActivityForm method from SearchRecordsCommand
    showAddActivityForm(table, record) {
        try {
            console.log(`Showing add activity form for ${table} record:`, record);

            const activityTable = this.getActivityTableName(table);
            const foreignKey = this.getForeignKeyName(table);

            console.log(`Activity table: ${activityTable}, Foreign key: ${foreignKey}`);

            // Get current user info
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                [foreignKey]: record.id,
                activity_date: new Date().toISOString().split('T')[0], // Today's date
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            console.log('Default data:', defaultData);

            // Check if schema is available
            if (!this.schema) {
                throw new Error('Schema manager not available');
            }

            // Generate form fields for the activity table, excluding system fields and foreign keys
            const excludeFields = ['id', 'created_at', 'created_by', foreignKey];
            const fields = this.schema.generateFormFields(activityTable, excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            console.log(`Generated ${fields.length} fields for activity form`);

            // Create and show the form
            this.ui.showFullScreenForm(
                `Add Activity - ${this.getRecordTitle(table, record)}`,
                fields,
                async (formData) => {
                    try {
                        // Merge default data with form data
                        const activityData = { ...defaultData, ...formData };

                        await this.data.insert(activityTable, activityData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing add activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    handleCreateNewPerson() {
        // Close the search modal
        document.querySelector('.modal-overlay').remove();
        
        // Show simplified person creation form
        this.showQuickPersonForm();
    }

    showQuickPersonForm() {
        // Create quick person creation modal
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Quick Add Person</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <p class="form-info">Enter basic information to create a new person record:</p>
                    <form class="quick-person-form" id="quick-person-form">
                        <div class="form-row">
                            <div class="form-field">
                                <label for="first_name">First Name *</label>
                                <input type="text" id="first_name" name="first_name" required>
                            </div>
                            <div class="form-field">
                                <label for="last_name">Last Name *</label>
                                <input type="text" id="last_name" name="last_name" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-field">
                                <label for="date_of_birth">Date of Birth</label>
                                <input type="date" id="date_of_birth" name="date_of_birth">
                            </div>
                            <div class="form-field">
                                <label for="phone">Phone Number</label>
                                <input type="text" id="phone" name="phone">
                            </div>
                        </div>
                        <div class="form-field">
                            <label for="notes">Quick Notes</label>
                            <textarea id="notes" name="notes" rows="2" placeholder="Any additional information about this person..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="primary-button" id="create-person-and-continue">Create Person & Continue</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle form submission
        const createBtn = modal.querySelector('#create-person-and-continue');
        const form = modal.querySelector('#quick-person-form');

        createBtn.addEventListener('click', async (e) => {
            e.preventDefault();
            
            try {
                createBtn.disabled = true;
                createBtn.textContent = 'Creating...';

                // Get form data
                const formData = new FormData(form);
                const personData = {
                    first_name: formData.get('first_name'),
                    last_name: formData.get('last_name'),
                    date_of_birth: formData.get('date_of_birth') || null,
                    phone: formData.get('phone') || null,
                    notes: formData.get('notes') || null,
                    created_by: this.auth.getCurrentUser()?.email,
                    created_at: new Date().toISOString()
                };

                // Validate required fields
                if (!personData.first_name || !personData.last_name) {
                    this.ui.showDialog('Validation Error', 'First Name and Last Name are required.', 'error');
                    return;
                }

                // Create the person record
                const newPerson = await this.data.insert('people', personData);

                // Close the modal
                modal.remove();

                // Show success message and continue to activity form
                if (window.app?.showToast) {
                    window.app.showToast(`✅ Person record created for ${personData.first_name} ${personData.last_name}`, 'success');
                }
                
                // Continue to activity form with the new person
                this.showAddActivityForm('people', newPerson);

            } catch (error) {
                console.error('Error creating person:', error);
                this.ui.showDialog('Error', `Failed to create person: ${error.message}`, 'error');
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'Create Person & Continue';
            }
        });

        // Focus first input
        modal.querySelector('#first_name').focus();
    }

    // Helper methods (reused from SearchRecordsCommand)
    getActivityTableName(recordTable) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities',
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordTable] || 'activities';
    }

    getForeignKeyName(table) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[table] || 'record_id';
    }

    getRecordTitle(table, record) {
        switch (table) {
            case 'people':
                return `${record.first_name || ''} ${record.last_name || ''}`.trim() || 'Unknown Person';
            case 'addresses':
                return record.street_address || 'Unknown Address';
            case 'license_plates':
                return record.plate_number || 'Unknown Plate';
            default:
                return `Record ${record.id}`;
        }
    }
}

// Items Management Command
class ItemsCommand extends BaseCommand {
    async execute(args) {
        return new Promise((resolve) => {
            this.showItemsInterface(resolve);
        });
    }

    showItemsInterface(resolve) {
        // Create items management modal
        const itemsModal = document.createElement('div');
        itemsModal.className = 'modal-overlay';

        itemsModal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>📦 Items Management</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="items-controls">
                        <button class="primary-button" id="add-item-btn">+ Add New Item</button>
                        <button class="secondary-button" id="low-stock-btn">⚠️ Low Stock Alert</button>
                        <button class="secondary-button" id="refresh-items-btn">🔄 Refresh</button>
                    </div>
                    <div class="items-list" id="items-list">
                        <div class="loading">Loading items...</div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(itemsModal);

        // Set up event handlers
        this.setupItemsHandlers(itemsModal, resolve);

        // Load items
        this.loadItems();
    }

    setupItemsHandlers(modal, resolve) {
        const addItemBtn = modal.querySelector('#add-item-btn');
        const lowStockBtn = modal.querySelector('#low-stock-btn');
        const refreshBtn = modal.querySelector('#refresh-items-btn');

        addItemBtn.addEventListener('click', () => {
            this.showAddItemForm();
        });

        lowStockBtn.addEventListener('click', () => {
            this.showLowStockAlert();
        });

        refreshBtn.addEventListener('click', () => {
            this.loadItems();
        });
    }

    async loadItems() {
        try {
            const itemsList = document.getElementById('items-list');
            if (!itemsList) return;

            itemsList.innerHTML = '<div class="loading">Loading items...</div>';

            const items = await this.data.search('items', {});

            if (!items || items.length === 0) {
                itemsList.innerHTML = '<div class="no-items">No items found. Click "Add New Item" to get started.</div>';
                return;
            }

            // Sort items by category and name
            items.sort((a, b) => {
                if (a.category !== b.category) {
                    return a.category.localeCompare(b.category);
                }
                return a.name.localeCompare(b.name);
            });

            const itemsHTML = items.map(item => this.formatItemRow(item)).join('');

            itemsList.innerHTML = `
                <div class="items-table">
                    <div class="items-header">
                        <div class="item-name">Name</div>
                        <div class="item-category">Category</div>
                        <div class="item-stock">Stock</div>
                        <div class="item-unit">Unit</div>
                        <div class="item-status">Status</div>
                        <div class="item-actions">Actions</div>
                    </div>
                    <div class="items-body">
                        ${itemsHTML}
                    </div>
                </div>
            `;

            // Set up item action handlers
            this.setupItemActionHandlers();

        } catch (error) {
            console.error('Error loading items:', error);
            const itemsList = document.getElementById('items-list');
            if (itemsList) {
                itemsList.innerHTML = `<div class="error">Failed to load items: ${error.message}</div>`;
            }
        }
    }

    formatItemRow(item) {
        const isLowStock = item.minimum_threshold && item.current_stock <= item.minimum_threshold;
        const stockClass = isLowStock ? 'low-stock' : '';
        const statusClass = item.active ? 'active' : 'inactive';

        return `
            <div class="item-row ${stockClass}" data-item-id="${item.id}">
                <div class="item-name">${item.name}</div>
                <div class="item-category">${item.category.replace('_', ' ')}</div>
                <div class="item-stock ${stockClass}">${item.current_stock}</div>
                <div class="item-unit">${item.unit_type}</div>
                <div class="item-status ${statusClass}">${item.active ? 'Active' : 'Inactive'}</div>
                <div class="item-actions">
                    <button class="action-btn edit-item-btn" data-item-id="${item.id}" title="Edit Item">✏️</button>
                    <button class="action-btn stock-btn" data-item-id="${item.id}" title="Update Stock">📦</button>
                    <button class="action-btn toggle-btn" data-item-id="${item.id}" title="${item.active ? 'Deactivate' : 'Activate'}">${item.active ? '🔴' : '🟢'}</button>
                </div>
            </div>
        `;
    }

    setupItemActionHandlers() {
        const itemsList = document.getElementById('items-list');
        if (!itemsList) return;

        itemsList.addEventListener('click', async (e) => {
            const itemId = e.target.dataset.itemId;
            if (!itemId) return;

            if (e.target.classList.contains('edit-item-btn')) {
                await this.showEditItemForm(itemId);
            } else if (e.target.classList.contains('stock-btn')) {
                await this.showUpdateStockForm(itemId);
            } else if (e.target.classList.contains('toggle-btn')) {
                await this.toggleItemStatus(itemId);
            }
        });
    }

    async showAddItemForm() {
        try {
            const fields = this.schema.generateFormFields('items', ['id', 'created_at', 'updated_at']);

            // Set default values
            fields.forEach(field => {
                if (field.name === 'active') {
                    field.value = true;
                } else if (field.name === 'current_stock') {
                    field.value = 0;
                }
            });

            this.ui.showFullScreenForm('Add New Item', fields, async (formData) => {
                try {
                    const itemData = {
                        ...formData,
                        created_at: new Date().toISOString(),
                        updated_at: new Date().toISOString()
                    };

                    await this.data.insert('items', itemData);
                    this.ui.showDialog('Success', 'Item added successfully!', 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to add item: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing add item form:', error);
            this.ui.showDialog('Error', `Failed to show add item form: ${error.message}`, 'error');
        }
    }

    async showEditItemForm(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const fields = this.schema.generateFormFields('items', ['id', 'created_at', 'updated_at']);

            // Set current values
            fields.forEach(field => {
                if (item[field.name] !== undefined) {
                    field.value = item[field.name];
                }
            });

            this.ui.showForm(`Edit Item: ${item.name}`, fields, async (formData) => {
                try {
                    const updateData = {
                        ...formData,
                        updated_at: new Date().toISOString()
                    };

                    await this.data.update('items', itemId, updateData);
                    this.ui.showDialog('Success', 'Item updated successfully!', 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update item: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing edit item form:', error);
            this.ui.showDialog('Error', `Failed to show edit item form: ${error.message}`, 'error');
        }
    }

    async showUpdateStockForm(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const fields = [
                {
                    name: 'action',
                    label: 'Action',
                    type: 'select',
                    required: true,
                    options: [
                        { value: 'add', label: 'Add Stock' },
                        { value: 'remove', label: 'Remove Stock' },
                        { value: 'set', label: 'Set Stock Level' }
                    ]
                },
                {
                    name: 'quantity',
                    label: 'Quantity',
                    type: 'number',
                    required: true,
                    min: 0
                },
                {
                    name: 'notes',
                    label: 'Notes',
                    type: 'textarea',
                    placeholder: 'Reason for stock change...'
                }
            ];

            this.ui.showForm(`Update Stock: ${item.name} (Current: ${item.current_stock})`, fields, async (formData) => {
                try {
                    let newStock = item.current_stock;

                    switch (formData.action) {
                        case 'add':
                            newStock += parseInt(formData.quantity);
                            break;
                        case 'remove':
                            newStock -= parseInt(formData.quantity);
                            break;
                        case 'set':
                            newStock = parseInt(formData.quantity);
                            break;
                    }

                    if (newStock < 0) {
                        this.ui.showDialog('Error', 'Stock cannot be negative', 'error');
                        return false;
                    }

                    await this.data.update('items', itemId, {
                        current_stock: newStock,
                        updated_at: new Date().toISOString()
                    });

                    this.ui.showDialog('Success', `Stock updated! New level: ${newStock} ${item.unit_type}`, 'success');
                    this.loadItems();
                    return true;
                } catch (error) {
                    this.ui.showDialog('Error', `Failed to update stock: ${error.message}`, 'error');
                    return false;
                }
            });
        } catch (error) {
            console.error('Error showing update stock form:', error);
            this.ui.showDialog('Error', `Failed to show update stock form: ${error.message}`, 'error');
        }
    }

    async toggleItemStatus(itemId) {
        try {
            const item = await this.data.get('items', itemId);
            if (!item) {
                this.ui.showDialog('Error', 'Item not found', 'error');
                return;
            }

            const newStatus = !item.active;
            const action = newStatus ? 'activate' : 'deactivate';

            const confirmed = confirm(`Are you sure you want to ${action} "${item.name}"?`);
            if (!confirmed) return;

            await this.data.update('items', itemId, {
                active: newStatus,
                updated_at: new Date().toISOString()
            });

            this.ui.showDialog('Success', `Item ${action}d successfully!`, 'success');
            this.loadItems();
        } catch (error) {
            console.error('Error toggling item status:', error);
            this.ui.showDialog('Error', `Failed to toggle item status: ${error.message}`, 'error');
        }
    }

    async showLowStockAlert() {
        try {
            // Force refresh items cache to ensure we have latest data
            await this.forceRefreshItems();
            
            const items = await this.data.search('items', { active: true });
            const lowStockItems = items.filter(item =>
                item.minimum_threshold && item.current_stock <= item.minimum_threshold
            );

            if (lowStockItems.length === 0) {
                this.ui.showDialog('Low Stock Alert', 'No items are currently below their minimum threshold.', 'info');
                return;
            }

            const alertHTML = lowStockItems.map(item => `
                <div class="low-stock-item">
                    <strong>${item.name}</strong><br>
                    Current: ${item.current_stock} ${item.unit_type}<br>
                    Minimum: ${item.minimum_threshold} ${item.unit_type}<br>
                    Category: ${item.category.replace('_', ' ')}
                </div>
            `).join('');

            const alertModal = document.createElement('div');
            alertModal.className = 'modal-overlay';
            alertModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>⚠️ Low Stock Alert</h3>
                        <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                    </div>
                    <div class="modal-body">
                        <p><strong>${lowStockItems.length} item(s) are below minimum threshold:</strong></p>
                        <div class="low-stock-list">
                            ${alertHTML}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="primary-button" onclick="this.closest('.modal-overlay').remove()">OK</button>
                    </div>
                </div>
            `;

            document.body.appendChild(alertModal);
        } catch (error) {
            console.error('Error showing low stock alert:', error);
            this.ui.showDialog('Error', `Failed to show low stock alert: ${error.message}`, 'error');
        }
    }
}

// Cleanup Updates Command
class CleanupUpdatesCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Checking update storage usage...', 'info');

            // Get current storage usage
            const storageInfo = await window.electronAPI.invoke('get-update-storage-usage');

            if (storageInfo.totalFiles === 0) {
                this.ui.showDialog(
                    'No Cleanup Needed',
                    'No temporary update files found.\n\n' +
                    'Your system is already optimized!',
                    'info'
                );
                this.ui.setStatus('No cleanup needed', 'info');
                return;
            }

            // Show storage usage and confirmation dialog
            const filesList = storageInfo.files.map(file =>
                `• ${file.name} (${file.formattedSize})`
            ).join('\n');

            const confirmed = confirm(
                `Update Storage Usage:\n\n` +
                `Files: ${storageInfo.totalFiles}\n` +
                `Total Size: ${storageInfo.formattedSize}\n\n` +
                `Files to be removed:\n${filesList}\n\n` +
                `This will free up ${storageInfo.formattedSize} of storage space.\n\n` +
                'This is safe and will not affect your application or data.\n\n' +
                'Continue with cleanup?'
            );

            if (!confirmed) {
                this.ui.setStatus('Cleanup cancelled', 'info');
                return;
            }

            this.ui.setStatus('Cleaning up update files...', 'info');

            // Perform cleanup
            await window.electronAPI.invoke('cleanup-updates');

            this.ui.showDialog(
                'Cleanup Complete',
                `Successfully cleaned up ${storageInfo.totalFiles} temporary update files.\n\n` +
                `Freed ${storageInfo.formattedSize} of storage space.\n\n` +
                'Your system is now optimized!',
                'success'
            );

            this.ui.setStatus('Update cleanup completed', 'success');

        } catch (error) {
            console.error('Error during update cleanup:', error);
            this.ui.showDialog(
                'Cleanup Failed',
                `Failed to clean up update files: ${error.message}\n\n` +
                'You can try again later or contact support if the problem persists.',
                'error'
            );
            this.ui.setStatus('Cleanup failed', 'error');
        }
    }
}



// Create Test Incident Command for testing map functionality

// Outreach Transaction Command - Opens dedicated outreach transaction screen
class OutreachTransactionCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Opening outreach transaction screen...', 'info');

            // Switch to outreach transaction screen
            await this.showOutreachTransactionScreen();

        } catch (error) {
            console.error('Error opening outreach transaction screen:', error);
            this.ui.showDialog('Error', `Failed to open outreach transactions: ${error.message}`, 'error');
            this.ui.setStatus('Failed to open outreach transactions', 'error');
        }
    }

    async showOutreachTransactionScreen() {
        // Don't try to get location during initialization to avoid popup errors
        // Let the user click the location pin button when they're ready
        const location = { address: '', coordinates: null };

        // Use extracted template
        const screenHTML = outreachFormTemplates.createOutreachTransactionScreen(location);

        // Replace main content with outreach transaction screen
        const contentArea = document.querySelector('.content-area');
        contentArea.innerHTML = screenHTML;

        // Get outreach manager from module registry
        const outreachModule = moduleRegistry.get('OutreachManagement');
        if (outreachModule && outreachModule.outreachManager) {
            window.outreachTransaction = outreachModule.outreachManager;
        } else {
            console.error('Outreach management module not available');
        }

        this.ui.setStatus('Outreach transaction screen loaded', 'success');
    }

    async getCurrentLocation() {
        try {
            console.log('🌍 Requesting native device location...');
            
            // First try native Windows location service via IPC
            if (window.require && window.require('electron')) {
                const { ipcRenderer } = window.require('electron');
                
                try {
                    console.log('🔄 Calling IPC get-native-location...');
                    const result = await ipcRenderer.invoke('get-native-location');
                    console.log('🔄 IPC result received:', result);
                    
                    if (result.success && result.location) {
                        const { latitude, longitude } = result.location;
                        console.log(`✅ Got native location: ${latitude}, ${longitude}`);
                        
                        try {
                            // Use Google Geocoding API to get address
                            const address = await this.geocodeCoordinates(latitude, longitude);
                            return {
                                address: address,
                                coordinates: `${latitude}, ${longitude}`
                            };
                        } catch (error) {
                            console.warn('Geocoding failed:', error);
                            return {
                                address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                                coordinates: `${latitude}, ${longitude}`
                            };
                        }
                    } else {
                        console.warn('Native location failed:', result.error);
                        
                        // Show helpful error message to user
                        if (result.error && result.error.includes('Location services are disabled')) {
                            const instructions = await ipcRenderer.invoke('get-location-setup-instructions');
                            throw new Error(`Location services are disabled.\n\n${instructions}`);
                        }
                    }
                } catch (error) {
                    console.warn('Native location IPC failed:', error);
                    
                    // If it's a permission or setup error, re-throw to show to user
                    if (error.message.includes('Location services') || error.message.includes('PowerShell')) {
                        throw error;
                    }
                }
            }
            
            // Try direct Windows location service through alternative IPC method
            if (window.require && window.require('electron')) {
                console.log('🌐 Trying direct Windows location service...');
                const { ipcRenderer } = window.require('electron');
                
                try {
                    // Call a simplified location service that bypasses navigator.geolocation
                    console.log('🔄 Calling direct location IPC...');
                    const directResult = await ipcRenderer.invoke('get-direct-location');
                    console.log('🔄 Direct location result:', directResult);
                    
                    if (directResult.success && directResult.location) {
                        const { latitude, longitude } = directResult.location;
                        console.log(`✅ Got direct location: ${latitude}, ${longitude}`);
                        
                        try {
                            const address = await this.geocodeCoordinates(latitude, longitude);
                            return {
                                address: address,
                                coordinates: `${latitude}, ${longitude}`
                            };
                        } catch (error) {
                            console.warn('Geocoding failed:', error);
                            return {
                                address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                                coordinates: `${latitude}, ${longitude}`
                            };
                        }
                    }
                } catch (error) {
                    console.warn('Direct location service failed:', error.message);
                }
            }
            
            // If all else fails, try a minimal navigator.geolocation call
            if (navigator.geolocation) {
                console.log('🌐 Final attempt with minimal geolocation...');
                try {
                    const position = await new Promise((resolve, reject) => {
                        // Use most permissive settings possible
                        navigator.geolocation.getCurrentPosition(resolve, reject, {
                            enableHighAccuracy: false,
                            timeout: 30000,
                            maximumAge: 600000 // 10 minutes
                        });
                    });
                    
                    const { latitude, longitude, accuracy } = position.coords;
                    console.log(`✅ Got minimal geolocation: ${latitude}, ${longitude} (accuracy: ${accuracy}m)`);

                    try {
                        const address = await this.geocodeCoordinates(latitude, longitude);
                        return {
                            address: address,
                            coordinates: `${latitude}, ${longitude}`
                        };
                    } catch (error) {
                        console.warn('Geocoding failed:', error);
                        return {
                            address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
                            coordinates: `${latitude}, ${longitude}`
                        };
                    }
                    
                } catch (error) {
                    console.warn('Minimal geolocation failed:', error.message);
                }
            }
            
            // Final fallback - prompt user for manual location input
            console.log('🔄 All automated methods failed, prompting for manual input...');
            return await this.promptForManualLocation();
            
        } catch (error) {
            console.warn('Location services not available:', error);
            return { address: 'Location services unavailable', coordinates: null };
        }
    }

    async promptForManualLocation() {
        try {
            console.log('📍 Prompting user for manual location input...');
            
            return new Promise((resolve) => {
                // Create manual location input dialog
                const modalHTML = `
                    <div class="modal-overlay" id="location-input-modal">
                        <div class="modal-dialog" style="max-width: 500px;">
                            <div class="modal-header">
                                <h3>📍 Enter Location Manually</h3>
                            </div>
                            <div class="modal-body">
                                <p style="color: #00ff00; margin-bottom: 15px;">
                                    Automated location detection failed. Please enter your current location:
                                </p>
                                <div class="form-group">
                                    <input type="text" 
                                           id="manual-location-input" 
                                           placeholder="e.g., 123 Main St, Toronto, ON or 43.6532, -79.3832"
                                           style="width: 100%; padding: 8px; background: #000; color: #00ff00; border: 1px solid #00ff00;">
                                </div>
                                <div class="form-group" style="margin-top: 10px;">
                                    <small style="color: #ffff00;">
                                        You can enter an address or coordinates (latitude, longitude)
                                    </small>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button id="location-ok-btn" class="btn btn-primary">OK</button>
                                <button id="location-skip-btn" class="btn btn-secondary">Skip Location</button>
                            </div>
                        </div>
                    </div>
                `;
                
                // Add modal to page
                document.body.insertAdjacentHTML('beforeend', modalHTML);
                const modal = document.getElementById('location-input-modal');
                const input = document.getElementById('manual-location-input');
                const okBtn = document.getElementById('location-ok-btn');
                const skipBtn = document.getElementById('location-skip-btn');
                
                // Focus the input
                setTimeout(() => input.focus(), 100);
                
                // Handle OK button
                okBtn.onclick = () => {
                    const location = input.value.trim();
                    modal.remove();
                    
                    if (location) {
                        // Check if it looks like coordinates (contains comma and numbers)
                        const coordPattern = /^-?\d+\.?\d*\s*,\s*-?\d+\.?\d*$/;
                        if (coordPattern.test(location)) {
                            const [lat, lng] = location.split(',').map(s => s.trim());
                            resolve({
                                address: `${parseFloat(lat).toFixed(6)}, ${parseFloat(lng).toFixed(6)}`,
                                coordinates: `${lat}, ${lng}`
                            });
                        } else {
                            resolve({
                                address: location,
                                coordinates: null
                            });
                        }
                    } else {
                        resolve({
                            address: 'Location not provided',
                            coordinates: null
                        });
                    }
                };
                
                // Handle Skip button
                skipBtn.onclick = () => {
                    modal.remove();
                    resolve({
                        address: 'Location skipped',
                        coordinates: null
                    });
                };
                
                // Handle Enter key
                input.onkeypress = (e) => {
                    if (e.key === 'Enter') {
                        okBtn.click();
                    }
                };
            });
            
        } catch (error) {
            console.error('Manual location prompt failed:', error);
            return {
                address: 'Location input failed',
                coordinates: null
            };
        }
    }

    async geocodeCoordinates(lat, lng) {
        const apiKey = this.app?.config?.get('google.apiKey');
        if (!apiKey) {
            throw new Error('Google API key not available');
        }

        const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=${apiKey}`
        );

        if (!response.ok) {
            throw new Error('Geocoding request failed');
        }

        const data = await response.json();

        if (data.status === 'OK' && data.results.length > 0) {
            return data.results[0].formatted_address;
        }

        throw new Error('No address found for coordinates');
    }

    async forceRefreshItems() {
        try {
            // Clear any cached data that might have invalid IDs
            this.data.clearCache();
            
            // Force refresh the items table from Supabase
            const supabase = this.data.getSupabaseClient();
            if (supabase && this.data.isOnline) {
                await this.data.refreshTableCache('items', supabase);
                console.log('✅ Items cache refreshed from Supabase in commands.js');
            }
        } catch (error) {
            console.warn('Warning: Could not force refresh items cache in commands.js:', error);
        }
    }
}

// Log Property Command
class LogPropertyCommand extends BaseCommand {
    constructor(manager) {
        super(manager);
        this.name = 'log-property';
        this.description = 'Log found/stolen/discarded property';
    }

    async execute() {
        try {
            // Call the property logging function from the main app
            await this.manager.app.showLogPropertyForm();
        } catch (error) {
            console.error('Error logging property:', error);
            this.ui.showDialog('Error', 'Failed to open property logging form: ' + error.message, 'error');
        }
    }
}

// Property Report Command
class PropertyReportCommand extends BaseCommand {
    constructor(manager) {
        super(manager);
        this.name = 'property-report';
        this.description = 'Generate property management reports';
    }

    async execute() {
        try {
            await this.showPropertyReportDialog();
        } catch (error) {
            console.error('Error generating property report:', error);
            this.ui.showDialog('Error', 'Failed to generate property report: ' + error.message, 'error');
        }
    }

    async showPropertyReportDialog() {
        const now = new Date();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const fields = [
            {
                name: 'report_type',
                type: 'select',
                label: 'Report Type',
                options: [
                    { value: 'summary', label: 'Summary Report' },
                    { value: 'overdue', label: 'Overdue Properties' },
                    { value: 'trends', label: 'Trends Analysis' },
                    { value: 'locations', label: 'Location Analytics' }
                ],
                required: true
            },
            {
                name: 'date_from',
                type: 'date',
                label: 'From Date',
                value: thirtyDaysAgo.toISOString().split('T')[0]
            },
            {
                name: 'date_to',
                type: 'date',
                label: 'To Date',
                value: now.toISOString().split('T')[0]
            },
            {
                name: 'status_filter',
                type: 'select',
                label: 'Status Filter (Optional)',
                options: [
                    { value: '', label: 'All Statuses' },
                    { value: 'found', label: 'Found' },
                    { value: 'investigating', label: 'Investigating' },
                    { value: 'returned', label: 'Returned' },
                    { value: 'handed_to_police', label: 'Handed to Police' }
                ]
            }
        ];

        this.ui.showForm('Generate Property Report', fields, async (formData) => {
            try {
                await this.generateReport(formData);
            } catch (error) {
                console.error('Error generating report:', error);
                this.ui.showDialog('Error', 'Failed to generate report: ' + error.message, 'error');
            }
        });
    }

    async generateReport(formData) {
        const { report_type, date_from, date_to, status_filter } = formData;
        const propertyManager = this.manager.app.propertyManager;

        let reportHTML = '';
        let reportTitle = '';

        switch (report_type) {
            case 'summary':
                const filters = status_filter ? { status: status_filter } : {};
                const report = await propertyManager.getPropertyReport(date_from, date_to, filters);
                reportTitle = 'Property Summary Report';
                reportHTML = this.generateSummaryReportHTML(report, date_from, date_to);
                break;

            case 'overdue':
                const overdueProperties = await propertyManager.getOverdueProperties();
                reportTitle = 'Overdue Properties Report';
                reportHTML = this.generateOverdueReportHTML(overdueProperties);
                break;

            case 'trends':
                const trends = await propertyManager.getPropertyTrends(30);
                reportTitle = 'Property Trends Analysis';
                reportHTML = this.generateTrendsReportHTML(trends);
                break;

            case 'locations':
                const locationAnalytics = await propertyManager.getLocationAnalytics();
                reportTitle = 'Location Analytics Report';
                reportHTML = this.generateLocationReportHTML(locationAnalytics);
                break;
        }

        this.ui.showDialog(reportTitle, reportHTML, 'info');
    }

    generateSummaryReportHTML(report, dateFrom, dateTo) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Property Summary Report</h3>
                    <p>Period: ${dateFrom} to ${dateTo}</p>
                </div>

                <div class="report-section">
                    <h4>Overview</h4>
                    <div class="report-stats">
                        <div class="stat-item">Total Properties: <strong>${report.totalProperties}</strong></div>
                        <div class="stat-item">Average Processing Time: <strong>${report.averageProcessingTime} days</strong></div>
                        <div class="stat-item">Overdue Properties: <strong>${report.overdueProperties.length}</strong></div>
                        <div class="stat-item">Recent Returns: <strong>${report.recentReturns.length}</strong></div>
                    </div>
                </div>

                <div class="report-section">
                    <h4>By Status</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.byStatus).map(([status, count]) =>
                            `<div class="breakdown-item">${status}: <strong>${count}</strong></div>`
                        ).join('')}
                    </div>
                </div>

                <div class="report-section">
                    <h4>By Property Type</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.byType).map(([type, count]) =>
                            `<div class="breakdown-item">${type}: <strong>${count}</strong></div>`
                        ).join('')}
                    </div>
                </div>

                <div class="report-section">
                    <h4>Top Locations</h4>
                    <div class="report-breakdown">
                        ${Object.entries(report.topLocations)
                            .sort(([,a], [,b]) => b - a)
                            .slice(0, 5)
                            .map(([location, count]) =>
                                `<div class="breakdown-item">${location}: <strong>${count}</strong></div>`
                            ).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    generateOverdueReportHTML(overdueProperties) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Overdue Properties Report</h3>
                    <p>Properties found more than 30 days ago and still pending</p>
                </div>

                <div class="report-section">
                    <h4>Overdue Properties (${overdueProperties.length})</h4>
                    <div class="overdue-list">
                        ${overdueProperties.length === 0 ?
                            '<div class="no-data">No overdue properties found!</div>' :
                            overdueProperties.map(property => `
                                <div class="overdue-item">
                                    <div class="property-number">${property.property_number}</div>
                                    <div class="property-details">
                                        <strong>${property.property_type}</strong> - ${property.description}
                                    </div>
                                    <div class="property-date">Found: ${property.found_date}</div>
                                    <div class="property-status status-${property.status}">${property.status}</div>
                                </div>
                            `).join('')
                        }
                    </div>
                </div>
            </div>
        `;
    }

    generateTrendsReportHTML(trends) {
        const sortedDates = Object.keys(trends).sort();

        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Property Trends Analysis</h3>
                    <p>Daily property activity over the last 30 days</p>
                </div>

                <div class="report-section">
                    <h4>Daily Activity</h4>
                    <div class="trends-list">
                        ${sortedDates.map(date => `
                            <div class="trend-item">
                                <div class="trend-date">${date}</div>
                                <div class="trend-stats">
                                    Found: <strong>${trends[date].found}</strong> |
                                    Returned: <strong>${trends[date].returned}</strong> |
                                    To Police: <strong>${trends[date].handed_to_police}</strong>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    generateLocationReportHTML(locationAnalytics) {
        return `
            <div class="property-report">
                <div class="report-header">
                    <h3>Location Analytics Report</h3>
                    <p>Top 10 locations where property is found</p>
                </div>

                <div class="report-section">
                    <h4>Top Locations</h4>
                    <div class="location-list">
                        ${Object.entries(locationAnalytics).map(([location, stats]) => `
                            <div class="location-item">
                                <div class="location-name"><strong>${location}</strong></div>
                                <div class="location-stats">Total: ${stats.total}</div>
                                <div class="location-breakdown">
                                    Types: ${Object.entries(stats.byType).map(([type, count]) => `${type}(${count})`).join(', ')}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
}

// Enhanced Property Return Command
class EnhancedPropertyReturnCommand extends BaseCommand {
    constructor(manager) {
        super(manager);
        this.name = 'enhanced-property-return';
        this.description = 'Return property with enhanced business/individual tracking';
    }

    async execute(args) {
        try {
            const propertyId = args.propertyId || args[0];
            
            if (!propertyId) {
                this.ui.showDialog('Error', 'Property ID is required', 'error');
                return;
            }

            // Delegate to app method for enhanced return modal
            await this.app.markPropertyAsReturned(propertyId);
            
        } catch (error) {
            console.error('Error executing enhanced property return:', error);
            this.ui.showDialog('Error', 'Failed to open property return form: ' + error.message, 'error');
        }
    }

    getHelp() {
        return 'enhanced-property-return <property-id> - Return property with enhanced business/individual tracking';
    }
}

// Clear Data Command - Clears all local cached data
class ClearDataCommand extends BaseCommand {
    async execute(args) {
        const confirmed = confirm('Are you sure you want to clear all local data? This cannot be undone.');
        if (confirmed) {
            try {
                // Clear SQLite cache if available
                if (this.app.data.sqlite) {
                    const cacheTables = [
                        'cache_people', 'cache_pets', 'cache_incidents',
                        'cache_addresses', 'cache_bikes', 'cache_encampments',
                        'cache_media', 'cache_items', 'cache_organizations'
                    ];
                    for (const table of cacheTables) {
                        this.app.data.sqlite.clear(table);
                    }
                }

                // Clear memory cache
                this.app.data.cache.clear();

                this.ui.showDialog('Success', 'All local data cleared.', 'success');
                this.ui.setStatus('Local data cleared');
            } catch (error) {
                this.ui.showDialog('Error', `Failed to clear data: ${error.message}`, 'error');
                this.ui.setStatus('Error clearing data');
            }
        }
    }

    getHelp() {
        return 'clear-data - Clear all local cached data (SQLite and memory cache)';
    }
}

// Reset Database Command - Force reset of SQLite database (removes corrupt database files)
class ResetDatabaseCommand extends BaseCommand {
    async execute(args) {
        const confirmed = confirm(`⚠️ WARNING: This will completely reset your local SQLite database!
        
All cached data will be lost and the database will be rebuilt from Supabase on next restart.

Are you absolutely sure you want to continue?`);
        
        if (!confirmed) {
            this.ui.setStatus('Database reset cancelled');
            return;
        }
        
        try {
            this.ui.setStatus('Resetting SQLite database...');
            
            // Close current SQLite connection
            if (this.app.data.sqlite) {
                this.app.data.sqlite.close();
            }
            
            // Use Node.js fs to remove database files
            const fs = window.require('fs');
            const path = window.require('path');
            const dataPath = this.app.data.config.getDataPath();
            
            const filesToRemove = [
                path.join(dataPath, 'cache.db'),
                path.join(dataPath, 'cache.db-wal'),
                path.join(dataPath, 'cache.db-shm')
            ];
            
            let removedCount = 0;
            filesToRemove.forEach(filePath => {
                try {
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                        removedCount++;
                        console.log(`✅ Removed ${path.basename(filePath)}`);
                    }
                } catch (error) {
                    console.warn(`⚠️ Could not remove ${filePath}:`, error.message);
                }
            });
            
            const message = `Database reset completed!

• ${removedCount} database file(s) removed
• The application will rebuild the database on next restart
• All schema conflicts have been resolved

Please restart the application now for changes to take effect.`;
            
            this.ui.showDialog('Database Reset Complete', message, 'success');
            this.ui.setStatus('Database reset complete - restart required');
            
        } catch (error) {
            console.error('Database reset failed:', error);
            this.ui.showDialog('Error', `Database reset failed: ${error.message}`, 'error');
            this.ui.setStatus('Database reset failed');
        }
    }

    getHelp() {
        return 'reset-db - Force reset of SQLite database (removes all cached data and fixes schema conflicts)';
    }
}

// Schema Synchronization Command - Manually sync SQLite schemas with Supabase
class SyncSchemasCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Synchronizing database schemas...');
            
            const results = await this.app.data.syncSchemas();
            
            if (results) {
                const message = `Schema synchronization completed:
                
• Tables checked: ${results.checked}
• Schemas matched: ${results.matched}  
• Tables fixed: ${results.fixed}
• Errors: ${results.errors}

${results.fixed > 0 ? 'Schema differences have been automatically resolved.' : 'All schemas are in sync.'}`;
                
                this.ui.showDialog('Schema Sync Complete', message, results.errors > 0 ? 'warning' : 'success');
                this.ui.setStatus(results.errors > 0 ? 'Schema sync completed with warnings' : 'Schemas synchronized');
            } else {
                this.ui.showDialog('Error', 'Schema sync manager not available', 'error');
                this.ui.setStatus('Schema sync failed');
            }
            
        } catch (error) {
            console.error('Schema sync command failed:', error);
            this.ui.showDialog('Error', `Schema synchronization failed: ${error.message}`, 'error');
            this.ui.setStatus('Schema sync failed');
        }
    }

    getHelp() {
        return 'sync-schemas - Manually synchronize SQLite cache schemas with Supabase database';
    }
}

// Schema Report Command - Generate detailed schema validation report
class SchemaReportCommand extends BaseCommand {
    async execute(args) {
        try {
            this.ui.setStatus('Generating schema validation report...');
            
            const report = await this.app.data.getSchemaSyncReport();
            
            if (report.error) {
                this.ui.showDialog('Error', report.error, 'error');
                this.ui.setStatus('Schema report failed');
                return;
            }
            
            // Format report for display
            const reportHtml = this.formatSchemaReport(report);
            
            // Show report in modal
            this.ui.showModal('Schema Validation Report', reportHtml, {
                size: 'large',
                allowHtml: true,
                showCloseButton: true
            });
            
            this.ui.setStatus('Schema validation report generated');
            
        } catch (error) {
            console.error('Schema report command failed:', error);
            this.ui.showDialog('Error', `Failed to generate schema report: ${error.message}`, 'error');
            this.ui.setStatus('Schema report failed');
        }
    }
    
    formatSchemaReport(report) {
        const timestamp = new Date(report.timestamp).toLocaleString();
        
        let html = `
            <div class="schema-report">
                <div class="report-header">
                    <h3>Database Schema Validation Report</h3>
                    <p><strong>Generated:</strong> ${timestamp}</p>
                </div>
                
                <div class="report-summary">
                    <h4>Summary</h4>
                    <div class="summary-stats">
                        <div class="stat-item">
                            <span class="stat-label">Total Tables:</span>
                            <span class="stat-value">${report.summary.total}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Schemas Matched:</span>
                            <span class="stat-value ${report.summary.matched === report.summary.total ? 'success' : 'warning'}">${report.summary.matched}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Need Synchronization:</span>
                            <span class="stat-value ${report.summary.needsSync === 0 ? 'success' : 'warning'}">${report.summary.needsSync}</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Errors:</span>
                            <span class="stat-value ${report.summary.errors === 0 ? 'success' : 'error'}">${report.summary.errors}</span>
                        </div>
                    </div>
                </div>
                
                <div class="report-details">
                    <h4>Table Details</h4>
                    <div class="table-list">
        `;
        
        // Add table details
        report.tables.forEach(table => {
            const status = table.matched ? 'matched' : (table.error ? 'error' : 'needs-sync');
            const statusText = table.matched ? 'Schema Matched' : (table.error ? 'Error' : 'Needs Sync');
            
            html += `
                <div class="table-item ${status}">
                    <div class="table-header">
                        <span class="table-name">${table.name}</span>
                        <span class="table-status ${status}">${statusText}</span>
                    </div>
            `;
            
            if (table.error) {
                html += `<div class="table-error">Error: ${table.error}</div>`;
            } else if (table.comparison && !table.comparison.isMatch) {
                html += `
                    <div class="table-details">
                        <div class="schema-stats">
                            <span>Supabase: ${table.comparison.supabaseColumnCount} columns</span>
                            <span>SQLite: ${table.comparison.sqliteColumnCount} columns</span>
                        </div>
                `;
                
                if (table.comparison.missingInSQLite.length > 0) {
                    html += `
                        <div class="missing-columns">
                            <strong>Missing in SQLite:</strong> ${table.comparison.missingInSQLite.map(col => col.name).join(', ')}
                        </div>
                    `;
                }
                
                if (table.comparison.typeMismatches.length > 0) {
                    html += `
                        <div class="type-mismatches">
                            <strong>Type Mismatches:</strong> ${table.comparison.typeMismatches.map(m => m.column).join(', ')}
                        </div>
                    `;
                }
                
                html += `</div>`;
            }
            
            html += `</div>`;
        });
        
        html += `
                    </div>
                </div>
            </div>
            
            <style>
                .schema-report { font-family: 'Courier New', monospace; color: #0f0; background: #000; padding: 20px; }
                .report-header h3 { color: #ff6b6b; margin-bottom: 10px; }
                .summary-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 20px; }
                .stat-item { display: flex; justify-content: space-between; padding: 5px; border: 1px solid #333; }
                .stat-value.success { color: #4ecdc4; }
                .stat-value.warning { color: #ffe66d; }
                .stat-value.error { color: #ff6b6b; }
                .table-item { border: 1px solid #333; margin-bottom: 10px; padding: 10px; }
                .table-item.matched { border-color: #4ecdc4; }
                .table-item.needs-sync { border-color: #ffe66d; }
                .table-item.error { border-color: #ff6b6b; }
                .table-header { display: flex; justify-content: space-between; margin-bottom: 5px; }
                .table-name { font-weight: bold; }
                .table-status.matched { color: #4ecdc4; }
                .table-status.needs-sync { color: #ffe66d; }
                .table-status.error { color: #ff6b6b; }
                .table-error { color: #ff6b6b; font-style: italic; }
                .schema-stats { margin-bottom: 5px; }
                .schema-stats span { margin-right: 15px; }
                .missing-columns, .type-mismatches { margin-top: 5px; font-size: 0.9em; }
            </style>
        `;
        
        return html;
    }

    getHelp() {
        return 'schema-report - Generate detailed schema validation report showing differences between SQLite and Supabase';
    }
}

// Add Service Barrier Command
class AddServiceBarrierCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        if (!personId) {
            throw new Error('Person ID is required');
        }

        // Get the person record
        const person = await this.data.get('people', personId);
        if (!person) {
            throw new Error('Person not found');
        }

        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('service_barriers', ['id', 'person_id', 'created_at', 'updated_at', 'created_by', 'updated_by']);

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Service Barrier', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('service_barriers', formData);
                    const barrierData = {
                        ...dbData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('service_barriers', barrierData);
                    this.ui.showDialog('Success', 'Service barrier added successfully!', 'success');
                    resolve(true);
                } catch (error) {
                    console.error('Error adding service barrier:', error);
                    this.ui.showDialog('Error', `Failed to add service barrier: ${error.message}`, 'error');
                    resolve(false);
                }
            });
        });
    }

    getHelp() {
        return 'add-service-barrier <person_id> - Add service barrier information for a person';
    }
}

// Add Support Contact Command
class AddSupportContactCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        if (!personId) {
            throw new Error('Person ID is required');
        }

        // Get the person record
        const person = await this.data.get('people', personId);
        if (!person) {
            throw new Error('Person not found');
        }

        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('support_contacts', ['id', 'person_id', 'created_at', 'updated_at', 'created_by', 'updated_by']);

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Support Contact', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('support_contacts', formData);
                    const contactData = {
                        ...dbData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('support_contacts', contactData);
                    this.ui.showDialog('Success', 'Support contact added successfully!', 'success');
                    resolve(true);
                } catch (error) {
                    console.error('Error adding support contact:', error);
                    this.ui.showDialog('Error', `Failed to add support contact: ${error.message}`, 'error');
                    resolve(false);
                }
            });
        });
    }

    getHelp() {
        return 'add-support-contact <person_id> - Add support contact information for a person';
    }
}

// Old criminal justice commands removed - replaced by Justice module

// Old criminal justice Add commands removed - replaced by Justice module

// Add Disability Command
class AddDisabilityCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        if (!personId) {
            throw new Error('Person ID is required');
        }

        // Get the person record
        const person = await this.data.get('people', personId);
        if (!person) {
            throw new Error('Person not found');
        }

        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('disabilities', ['id', 'person_id', 'created_at', 'updated_at', 'created_by', 'updated_by']);

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Disability', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('disabilities', formData);
                    const disabilityData = {
                        ...dbData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('disabilities', disabilityData);
                    this.ui.showDialog('Success', 'Disability information added successfully!', 'success');
                    resolve(true);
                } catch (error) {
                    console.error('Error adding disability:', error);
                    this.ui.showDialog('Error', `Failed to add disability information: ${error.message}`, 'error');
                    resolve(false);
                }
            });
        });
    }

    getHelp() {
        return 'add-disability <person_id> - Add disability information for a person';
    }
}

// Add Case Management Command
class AddCaseManagementCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        if (!personId) {
            throw new Error('Person ID is required');
        }

        // Get the person record
        const person = await this.data.get('people', personId);
        if (!person) {
            throw new Error('Person not found');
        }

        // Generate fields from schema
        const fields = this.data.schema.generateFormFields('case_management', ['id', 'person_id', 'created_at', 'updated_at', 'created_by', 'updated_by']);

        return new Promise((resolve) => {
            this.ui.showFullScreenForm('Add Case Management Information', fields, async (formData) => {
                try {
                    // Convert form data to database format
                    const dbData = this.data.schema.convertFormToDatabase('case_management', formData);
                    const caseData = {
                        ...dbData,
                        person_id: personId,
                        created_by: this.auth.getCurrentUser()?.email,
                        created_at: new Date().toISOString()
                    };

                    await this.data.insert('case_management', caseData);
                    this.ui.showDialog('Success', 'Case management information added successfully!', 'success');
                    
                    // Refresh the person detail view
                    if (this.app.loadPersonCaseManagement) {
                        await this.app.loadPersonCaseManagement(personId);
                    }
                    
                    resolve(true);
                } catch (error) {
                    console.error('Error adding case management:', error);
                    this.ui.showDialog('Error', `Failed to add case management information: ${error.message}`, 'error');
                    resolve(false);
                }
            });
        });
    }

    getHelp() {
        return 'add-case-management <person_id> - Add case management information for a person';
    }
}

// Delete Service Barrier Command
class DeleteServiceBarrierCommand extends BaseCommand {
    async execute(args) {
        const recordId = args[0];
        if (!recordId) {
            throw new Error('Record ID is required');
        }

        return new Promise((resolve) => {
            this.ui.showConfirmDialog(
                'Confirm Delete',
                'Are you sure you want to delete this service barrier record?',
                async () => {
                    try {
                        await this.data.delete('service_barriers', recordId);
                        this.ui.showDialog('Success', 'Service barrier deleted successfully!', 'success');
                        
                        // Refresh the service barriers tab if person is selected
                        if (this.app.selectedPerson) {
                            await this.app.loadPersonServiceBarriers(this.app.selectedPerson.id);
                        }
                        
                        resolve(true);
                    } catch (error) {
                        console.error('Error deleting service barrier:', error);
                        this.ui.showDialog('Error', `Failed to delete service barrier: ${error.message}`, 'error');
                        resolve(false);
                    }
                },
                () => resolve(false)
            );
        });
    }

    getHelp() {
        return 'delete-service-barrier <record_id> - Delete a service barrier record';
    }
}

// Delete Support Contact Command
class DeleteSupportContactCommand extends BaseCommand {
    async execute(args) {
        const recordId = args[0];
        if (!recordId) {
            throw new Error('Record ID is required');
        }

        return new Promise((resolve) => {
            this.ui.showConfirmDialog(
                'Confirm Delete',
                'Are you sure you want to delete this support contact record?',
                async () => {
                    try {
                        await this.data.delete('support_contacts', recordId);
                        this.ui.showDialog('Success', 'Support contact deleted successfully!', 'success');
                        
                        // Refresh the support network tab if person is selected
                        if (this.app.selectedPerson) {
                            await this.app.loadPersonSupportNetwork(this.app.selectedPerson.id);
                        }
                        
                        resolve(true);
                    } catch (error) {
                        console.error('Error deleting support contact:', error);
                        this.ui.showDialog('Error', `Failed to delete support contact: ${error.message}`, 'error');
                        resolve(false);
                    }
                },
                () => resolve(false)
            );
        });
    }

    getHelp() {
        return 'delete-support-contact <record_id> - Delete a support contact record';
    }
}

// Old criminal justice Delete commands removed - replaced by Justice module

// Delete Disability Command
class DeleteDisabilityCommand extends BaseCommand {
    async execute(args) {
        const recordId = args[0];
        if (!recordId) {
            throw new Error('Record ID is required');
        }

        return new Promise((resolve) => {
            this.ui.showConfirmDialog(
                'Confirm Delete',
                'Are you sure you want to delete this disability record?',
                async () => {
                    try {
                        await this.data.delete('disabilities', recordId);
                        this.ui.showDialog('Success', 'Disability record deleted successfully!', 'success');
                        
                        // Refresh the disabilities tab if person is selected
                        if (this.app.selectedPerson) {
                            await this.app.loadPersonDisabilities(this.app.selectedPerson.id);
                        }
                        
                        resolve(true);
                    } catch (error) {
                        console.error('Error deleting disability:', error);
                        this.ui.showDialog('Error', `Failed to delete disability: ${error.message}`, 'error');
                        resolve(false);
                    }
                },
                () => resolve(false)
            );
        });
    }

    getHelp() {
        return 'delete-disability <record_id> - Delete a disability record';
    }
}

// Delete Case Management Command
class DeleteCaseManagementCommand extends BaseCommand {
    async execute(args) {
        const recordId = args[0];
        if (!recordId) {
            throw new Error('Record ID is required');
        }

        return new Promise((resolve) => {
            this.ui.showConfirmDialog(
                'Confirm Delete',
                'Are you sure you want to delete this case management record?',
                async () => {
                    try {
                        await this.data.delete('case_management', recordId);
                        this.ui.showDialog('Success', 'Case management record deleted successfully!', 'success');
                        
                        // Refresh the case management tab if person is selected
                        if (this.app.selectedPerson) {
                            await this.app.loadPersonCaseManagement(this.app.selectedPerson.id);
                        }
                        
                        resolve(true);
                    } catch (error) {
                        console.error('Error deleting case management:', error);
                        this.ui.showDialog('Error', `Failed to delete case management: ${error.message}`, 'error');
                        resolve(false);
                    }
                },
                () => resolve(false)
            );
        });
    }

    getHelp() {
        return 'delete-case-management <record_id> - Delete a case management record';
    }
}

// Add Person Activity Command - Direct activity creation for a specific person
class AddPersonActivityCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        
        if (personId) {
            // Direct person activity creation
            this.showPersonActivityForm(personId);
        } else {
            // No person specified, show person search first
            this.showPersonSearch();
        }
    }

    showPersonSearch() {
        const searchModal = document.createElement('div');
        searchModal.className = 'modal-overlay';

        searchModal.innerHTML = `
            <div class="modal-dialog search-modal">
                <div class="modal-header">
                    <h3>Select Person for Activity</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="search-form">
                        <div class="form-field">
                            <label for="person-search-query">Search For Person:</label>
                            <input type="text" id="person-search-query" name="person-search-query" placeholder="Enter name, phone, email...">
                        </div>
                        <div class="search-actions">
                            <button type="button" id="person-search-submit" class="primary-button">Search</button>
                            <button type="button" id="create-new-person" class="secondary-button">Create New Person</button>
                        </div>
                    </div>
                    <div class="search-results" id="person-search-results" style="display: none;">
                        <h4>Search Results:</h4>
                        <div class="results-container" id="person-results-container"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                </div>
            </div>
        `;

        document.body.appendChild(searchModal);

        const searchQuery = searchModal.querySelector('#person-search-query');
        const searchSubmit = searchModal.querySelector('#person-search-submit');
        const createNewBtn = searchModal.querySelector('#create-new-person');
        const searchResults = searchModal.querySelector('#person-search-results');
        const resultsContainer = searchModal.querySelector('#person-results-container');

        // Handle search submission
        const performSearch = async () => {
            const query = searchQuery.value.trim();

            if (!query) {
                this.ui.showDialog('Search Error', 'Please enter a search term.', 'error');
                return;
            }

            try {
                searchSubmit.disabled = true;
                searchSubmit.textContent = 'Searching...';

                const results = await this.searchPeople(query);
                this.displayPersonResults(results, resultsContainer);
                searchResults.style.display = 'block';

            } catch (error) {
                this.ui.showDialog('Search Error', `Search failed: ${error.message}`, 'error');
            } finally {
                searchSubmit.disabled = false;
                searchSubmit.textContent = 'Search';
            }
        };

        // Handle create new person
        const handleCreateNewPerson = async () => {
            try {
                // Close the search modal
                searchModal.remove();

                // Show person creation form
                await this.showCreatePersonForm();
            } catch (error) {
                console.error('Error creating new person:', error);
                this.ui.showDialog('Error', 'Failed to show person creation form', 'error');
            }
        };

        searchSubmit.addEventListener('click', performSearch);
        createNewBtn.addEventListener('click', handleCreateNewPerson);
        searchQuery.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        searchQuery.focus();
    }

    async searchPeople(query) {
        try {
            const allPeople = await this.data.search('people', {});
            if (!allPeople || allPeople.length === 0) {
                return [];
            }

            const lowerQuery = query.toLowerCase();
            
            // Only search in relevant person fields, not system fields
            const searchFields = ['first_name', 'last_name', 'nickname', 'phone', 'email', 'emergency_contact_name', 'emergency_contact_phone'];
            
            return allPeople.filter(person => {
                return searchFields.some(fieldName => {
                    const value = person[fieldName];
                    if (typeof value === 'string' && value.trim()) {
                        return value.toLowerCase().includes(lowerQuery);
                    }
                    return false;
                });
            });
        } catch (error) {
            console.error('Error searching people:', error);
            return [];
        }
    }

    displayPersonResults(results, container) {
        container.innerHTML = '';

        if (results.length === 0) {
            container.innerHTML = '<div class="no-results">No people found matching your search.</div>';
            return;
        }

        results.forEach(person => {
            const personDiv = document.createElement('div');
            personDiv.className = 'record-item selectable';
            personDiv.innerHTML = `
                <div class="record-summary">
                    <strong>${person.first_name || ''} ${person.last_name || ''}</strong>
                    ${person.date_of_birth ? `<br>DOB: ${person.date_of_birth}` : ''}
                    ${person.phone ? `<br>Phone: ${person.phone}` : ''}
                    ${person.email ? `<br>Email: ${person.email}` : ''}
                </div>
                <button class="select-person-btn primary-button" data-person-id="${person.id}">Select</button>
            `;

            const selectBtn = personDiv.querySelector('.select-person-btn');
            selectBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                // Find and remove the specific search modal
                const searchModal = e.target.closest('.modal-overlay');
                if (searchModal) {
                    searchModal.remove();
                }
                this.showPersonActivityForm(person.id);
            });

            container.appendChild(personDiv);
        });
    }

    async showPersonActivityForm(personId) {
        try {
            // Get person info for display
            const people = await this.data.search('people', { id: personId });
            const person = people[0];
            
            if (!person) {
                this.ui.showDialog('Error', 'Person not found.', 'error');
                return;
            }

            // Get current user info
            const currentUser = this.auth.getCurrentUser();

            // Pre-fill some fields
            const defaultData = {
                person_id: person.id,
                activity_date: new Date().toISOString().split('T')[0],
                staff_member: currentUser?.name || currentUser?.email || 'Unknown Staff',
                created_by: currentUser?.email || 'unknown'
            };

            // Generate form fields for people_activities table
            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'title'];
            const fields = this.schema.generateFormFields('people_activities', excludeFields);

            // Set default values in the fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
            });

            const personName = `${person.first_name || ''} ${person.last_name || ''}`.trim() || 'Unknown Person';

            // Create and show the form
            this.ui.showFullScreenForm(
                `Add Activity - ${personName}`,
                fields,
                async (formData) => {
                    try {
                        // Merge default data with form data
                        const activityData = { ...defaultData, ...formData };
                        
                        // Auto-generate title from activity_type
                        if (activityData.activity_type) {
                            activityData.title = activityData.activity_type;
                        }

                        await this.data.insert('people_activities', activityData);
                        this.ui.showDialog('Success', 'Activity added successfully!', 'success');

                        // Refresh activities if person detail is open
                        if (this.app.selectedPerson && this.app.selectedPerson.id === personId) {
                            await this.app.loadPersonActivities(personId);
                        }

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to add activity: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing person activity form:', error);
            this.ui.showDialog('Error', `Failed to show activity form: ${error.message}`, 'error');
        }
    }

    async showCreatePersonForm() {
        try {
            // Generate form fields for person creation
            const fields = this.schema.generateFormFields('people');

            this.ui.showForm('Create New Person', fields, async (formData) => {
                try {
                    const currentUser = this.auth.getCurrentUser();
                    const personData = {
                        ...formData,
                        created_at: new Date().toISOString(),
                        created_by: currentUser?.email || 'System'
                    };

                    const newPerson = await this.data.insert('people', personData);
                    this.ui.showDialog('Success', 'Person created successfully!', 'success');

                    // Automatically show activity form for the new person
                    setTimeout(() => {
                        this.showPersonActivityForm(newPerson.id);
                    }, 500);

                    return true; // Close the form
                } catch (error) {
                    console.error('Error creating person:', error);
                    this.ui.showDialog('Error', 'Failed to create person record', 'error');
                    return false; // Keep the form open
                }
            });
        } catch (error) {
            console.error('Error showing person creation form:', error);
            this.ui.showDialog('Error', 'Failed to show person creation form', 'error');
        }
    }

    getHelp() {
        return 'add-person-activity [person_id] - Add an activity for a person (prompts for person selection if not specified)';
    }
}

// Specialized medical issue commands

class AddInfectionTrackingCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        
        if (!personId) {
            this.ui.showDialog('Error', 'Person ID is required for infection tracking.', 'error');
            return;
        }

        try {
            const currentUser = this.auth.getCurrentUser();
            
            // Pre-populate infection-specific fields
            const defaultData = {
                person_id: personId,
                category: 'Infection',
                source_type: 'Observation',
                is_active: true,
                created_by: currentUser?.email || 'unknown'
            };

            // Generate form fields specifically for infection tracking
            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'category'];
            const fields = this.schema.generateFormFields('medical_issues', excludeFields);

            // Set default values and adjust specific fields
            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
                
                // Make infection-specific fields more prominent
                if (field.name === 'subcategory') {
                    field.options = [
                        'Abscess', 'Cellulitis', 'Endocarditis', 'Skin Infection', 'Blood Infection',
                        'Xylazine-Related Wound', 'Needle-Related Infection', 'Other'
                    ];
                }
            });

            this.ui.showFullScreenForm(
                '🦠 Track Drug-Related Infection',
                fields,
                async (formData) => {
                    try {
                        const infectionData = { ...defaultData, ...formData };

                        await this.data.insert('medical_issues', infectionData);
                        this.ui.showDialog('Success', 'Infection tracking record created successfully!', 'success');

                        // Refresh medical issues if person detail is open
                        if (this.app.selectedPerson && this.app.selectedPerson.id === personId) {
                            await this.app.loadPersonMedicalIssues(personId);
                        }

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to create infection record: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing infection tracking form:', error);
            this.ui.showDialog('Error', `Failed to show infection tracking form: ${error.message}`, 'error');
        }
    }

    getHelp() {
        return 'add-infection-tracking <person_id> - Track drug-related infections and wounds';
    }
}

class AddMentalHealthNoteCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        
        if (!personId) {
            this.ui.showDialog('Error', 'Person ID is required for mental health tracking.', 'error');
            return;
        }

        try {
            const currentUser = this.auth.getCurrentUser();
            
            // Pre-populate mental health specific fields
            const defaultData = {
                person_id: personId,
                category: 'Mental Health',
                source_type: 'Observation',
                is_active: true,
                created_by: currentUser?.email || 'unknown'
            };

            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'category'];
            const fields = this.schema.generateFormFields('medical_issues', excludeFields);

            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
                
                if (field.name === 'subcategory') {
                    field.options = [
                        'Depression', 'Anxiety', 'Bipolar Disorder', 'Schizophrenia', 'PTSD',
                        'Personality Disorder', 'Psychosis', 'Suicidal Ideation', 'Self-Harm',
                        'Cognitive Impairment', 'Other'
                    ];
                }
            });

            this.ui.showFullScreenForm(
                '🧠 Mental Health Assessment',
                fields,
                async (formData) => {
                    try {
                        const mentalHealthData = { ...defaultData, ...formData };

                        await this.data.insert('medical_issues', mentalHealthData);
                        this.ui.showDialog('Success', 'Mental health note created successfully!', 'success');

                        if (this.app.selectedPerson && this.app.selectedPerson.id === personId) {
                            await this.app.loadPersonMedicalIssues(personId);
                        }

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to create mental health note: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing mental health form:', error);
            this.ui.showDialog('Error', `Failed to show mental health form: ${error.message}`, 'error');
        }
    }

    getHelp() {
        return 'add-mental-health-note <person_id> - Record mental health observations and assessments';
    }
}

class AddAddictionNoteCommand extends BaseCommand {
    async execute(args) {
        const personId = args.personId || args[0];
        
        if (!personId) {
            this.ui.showDialog('Error', 'Person ID is required for addiction tracking.', 'error');
            return;
        }

        try {
            const currentUser = this.auth.getCurrentUser();
            
            // Pre-populate addiction specific fields
            const defaultData = {
                person_id: personId,
                category: 'Addiction',
                source_type: 'Self-Reported',
                is_active: true,
                created_by: currentUser?.email || 'unknown'
            };

            const excludeFields = ['id', 'created_at', 'created_by', 'person_id', 'category'];
            const fields = this.schema.generateFormFields('medical_issues', excludeFields);

            fields.forEach(field => {
                if (defaultData[field.name] !== undefined) {
                    field.value = defaultData[field.name];
                }
                
                if (field.name === 'subcategory') {
                    field.options = [
                        'Opioid Use', 'Stimulant Use', 'Alcohol Use', 'Cannabis Use',
                        'Polydrug Use', 'Withdrawal', 'Overdose History', 'Injection Site Issues', 'Other'
                    ];
                }
            });

            this.ui.showFullScreenForm(
                '💊 Addiction & Substance Use Assessment',
                fields,
                async (formData) => {
                    try {
                        const addictionData = { ...defaultData, ...formData };

                        await this.data.insert('medical_issues', addictionData);
                        this.ui.showDialog('Success', 'Addiction assessment created successfully!', 'success');

                        if (this.app.selectedPerson && this.app.selectedPerson.id === personId) {
                            await this.app.loadPersonMedicalIssues(personId);
                        }

                        return true;
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to create addiction note: ${error.message}`, 'error');
                        return false;
                    }
                }
            );
        } catch (error) {
            console.error('Error showing addiction form:', error);
            this.ui.showDialog('Error', `Failed to show addiction form: ${error.message}`, 'error');
        }
    }

    getHelp() {
        return 'add-addiction-note <person_id> - Track substance use and addiction-related issues';
    }
}

class ViewWeatherAlertsCommand extends BaseCommand {
    async execute(args) {
        return new Promise(async (resolve) => {
            try {
                console.log('ViewWeatherAlertsCommand: Getting current weather data...');
                const weatherData = await this.app.weather.getCurrentWeather();
                const alerts = this.app.weather.formatAlertsForDisplay(weatherData.alerts || []);
                
                await this.app.showWeatherAlertsModal(alerts, weatherData);
                resolve(true);
            } catch (error) {
                console.error('Error viewing weather alerts:', error);
                this.ui.showDialog('Error', `Failed to load weather alerts: ${error.message}`, 'error');
                resolve(null);
            }
        });
    }

    getHelp() {
        return 'view-weather-alerts - Display all active weather alerts in detail';
    }
}

class DeleteOutreachTransactionCommand extends BaseCommand {
    async execute(args) {
        const transactionId = args?.transactionId || args[0];
        if (!transactionId) {
            this.ui.showDialog('Error', 'Transaction ID is required for deletion', 'error');
            return;
        }

        try {
            // Get transaction details for confirmation
            const transaction = await this.data.get('outreach_transactions', transactionId);
            if (!transaction) {
                this.ui.showDialog('Error', 'Outreach transaction not found', 'error');
                return;
            }

            const transactionDescription = `Transaction #${String(transaction.id).substring(0, 8)} for ${transaction.person_name}`;

            // Show confirmation dialog
            const confirmed = await this.showDeleteConfirmation(transactionDescription);
            if (!confirmed) {
                return;
            }

            // Delete the outreach transaction
            await this.data.delete('outreach_transactions', transactionId);

            this.ui.showDialog('Success', `Outreach transaction deleted successfully.`, 'success');

            // Refresh the outreach list if we're on the outreach tab
            if (this.app.refreshOutreachList) {
                this.app.refreshOutreachList();
            }

            // Clear the details view
            const detailsSection = document.getElementById('outreach-details');
            if (detailsSection) {
                detailsSection.innerHTML = `
                    <div class="no-selection">
                        <h3>Select a Transaction</h3>
                        <p>Choose a transaction from the list to view details.</p>
                    </div>
                `;
            }

        } catch (error) {
            console.error('Error deleting outreach transaction:', error);
            this.ui.showDialog('Error', `Failed to delete transaction: ${error.message}`, 'error');
        }
    }

    async showDeleteConfirmation(description) {
        return new Promise((resolve) => {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-header">
                        <h3>⚠️ Confirm Deletion</h3>
                    </div>
                    <div class="modal-body">
                        <p><strong>Are you sure you want to delete this outreach transaction?</strong></p>
                        <p>${description}</p>
                        <p class="warning-text">⚠️ This action cannot be undone!</p>
                    </div>
                    <div class="modal-footer">
                        <button class="secondary-button" onclick="this.closest('.modal-overlay').remove(); window.deleteConfirmResolve(false);">Cancel</button>
                        <button class="danger-button" onclick="this.closest('.modal-overlay').remove(); window.deleteConfirmResolve(true);">Delete Transaction</button>
                    </div>
                </div>
            `;

            window.deleteConfirmResolve = resolve;
            document.body.appendChild(modal);
        });
    }

    getHelp() {
        return 'delete-outreach-transaction <transaction-id> - Delete an outreach transaction';
    }
}

// CommandManager is already exported at the top of the file
