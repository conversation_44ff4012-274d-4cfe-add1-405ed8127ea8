/**
 * Activity Management Module
 * Provides modular activity management functionality
 */

import { FeatureModuleInterface } from '../../shared/module-interface.js';
import { ActivityManager } from './activity-manager.js';
import { ActivitySearchManager } from './activity-search-manager.js';

export class ActivityManagement extends FeatureModuleInterface {
    constructor(dataManager, authManager, uiManager, uiUtilities, modalManagement = null) {
        super('ActivityManagement', '1.0.0', [], ['activities']);
        this.data = dataManager;
        this.auth = authManager;
        this.ui = uiManager;
        this.uiUtilities = uiUtilities;
        this.modalManagement = modalManagement;

        // Initialize managers
        this.activityManager = new ActivityManager(dataManager, authManager, uiManager, uiUtilities);
        this.searchManager = new ActivitySearchManager(dataManager, authManager, uiManager, uiUtilities);
    }

    // === MAIN WORKFLOWS ===

    /**
     * Show activity type selection modal (main entry point)
     */
    async showActivityTypeSelection() {
        return await this.activityManager.showActivityTypeSelection();
    }

    /**
     * Show record search for specific activity type
     */
    async showRecordSearch(activityType) {
        return await this.searchManager.showRecordSearch(activityType);
    }

    /**
     * Show activity form for selected record
     */
    async showActivityForm(recordType, record) {
        return await this.activityManager.showActivityForm(recordType, record);
    }

    // === DELEGATED METHODS ===

    // Activity Manager methods
    async addActivity(recordType, recordId, activityData) {
        return await this.activityManager.addActivity(recordType, recordId, activityData);
    }

    async loadActivities(recordType, recordId) {
        return await this.activityManager.loadActivities(recordType, recordId);
    }

    // Search Manager methods
    async searchRecords(recordType, query) {
        return await this.searchManager.searchRecords(recordType, query);
    }

    async showPersonCreationForm() {
        return await this.searchManager.showPersonCreationForm();
    }

    // === UTILITY METHODS ===

    getActivityTableName(recordType) {
        const mapping = {
            'people': 'people_activities',
            'addresses': 'address_activities', 
            'license_plates': 'vehicle_activities'
        };
        return mapping[recordType] || 'activities';
    }

    getForeignKeyName(recordType) {
        const mapping = {
            'people': 'person_id',
            'addresses': 'address_id',
            'license_plates': 'vehicle_id'
        };
        return mapping[recordType] || 'record_id';
    }

    /**
     * Initialize the activity management module
     * @returns {Promise<void>}
     */
    async initialize() {
        console.log('Initializing Activity Management module');
        this.initialized = true;
    }

    /**
     * Cleanup method
     */
    cleanup() {
        console.log('Cleaning up Activity Management module');
        if (this.activityManager?.cleanup) {
            this.activityManager.cleanup();
        }
        if (this.searchManager?.cleanup) {
            this.searchManager.cleanup();
        }
        this.initialized = false;
    }

    /**
     * Get commands provided by this module
     * @param {Object} commandManager - Command manager instance
     * @returns {Map} Map of command name to command class
     */
    getCommands(commandManager) {
        // TODO: Implement activity management commands
        return new Map();
    }
}

export { ActivityManager, ActivitySearchManager };
