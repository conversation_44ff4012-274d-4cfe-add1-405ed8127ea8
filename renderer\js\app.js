// S.T.E.V.I Retro - Main Application
import { AuthManager } from './auth.js';
import { UIManager } from './ui.js';
import { DataManager } from './data.js';
import { CommandManager } from './commands.js';
import { moduleRegistry } from '../../modules/shared/module-registry.js';
import { ConfigManager } from './config.js';
import { UpdateUIManager } from './updater.js';
import { WeatherService } from './weather.js';
import { LocationManager } from './location-manager.js';

import { SecurityHeadersManager } from './security-headers.js';
import { SecurityErrorHandler } from './security-error-handler.js';
import { SecurityMonitor } from './security-monitor.js';

import { AdminManager } from './admin.js';
import { VaultDiagnostics } from './vault-diagnostics.js';
import { GoogleMapsAddressSearch } from './google-maps-address.js';
import { supabaseConfig } from './supabase-config.js';
import { ReportsManager } from '../../modules/reports-management/js/index.js';
import { UIUtilities } from './modules/ui-utilities/index.js';
import { ModalManagement } from './modules/modal-management/index.js';
import { IncidentManagement } from '../../modules/incident-management/js/index.js';
import { VehicleManagement } from '../../modules/vehicle-management/js/index.js';
import { PeopleManagement } from '../../modules/people-management/js/index.js';
import { ActivityManagement } from '../../modules/activity-management/js/index.js';
import { OrganizationManagement } from '../../modules/organizations-management/js/index.js';
import { OutreachManagement } from '../../modules/outreach-management/js/index.js';
import { ReportsManagement } from '../../modules/reports-management/js/index.js';
import { SystemSettings } from '../../modules/system-settings/js/index.js';

import { JusticeModule } from '../../modules/justice/js/index.js';
import { encampmentTemplates } from '../../modules/encampment-management/templates/encampment-templates.js';
import { outreachListTemplates, outreachDetailTemplates } from '../../modules/outreach-management/templates/index.js';
import { reportTemplates } from '../../modules/reports-management/templates/index.js';
import { systemSettingsTemplates, systemSettingsModals } from '../../modules/system-settings/templates/index.js';
import { vehicleListTemplates } from '../../modules/vehicle-management/templates/vehicle-list-templates.js';
import { vehicleManagementTemplates } from '../../modules/vehicle-management/templates/vehicle-management-templates.js';
import { organizationListTemplates } from '../../modules/organizations-management/templates/organization-list-templates.js';
import { addressSearchTemplates } from '../../modules/address-management/templates/address-search-templates.js';
import { addressFormTemplates } from '../../modules/address-management/templates/address-form-templates.js';
import { organizationSearchTemplates } from '../../modules/organizations-management/templates/organization-search-templates.js';
import { propertyReturnTemplates } from '../../modules/property-management/templates/index.js';
import { statusRibbonTemplates, startEpisodeTemplates, timelineTemplates, addEventTemplates, chargesTemplates, conditionsTemplates, contactsTemplates } from '../../modules/justice/templates/index.js';
import { SystemSettingsManager } from '../../modules/system-settings/js/index.js';
import { mountJusticeTab } from '../../modules/justice/js/index.js';
// Migration system removed for simplicity

class SteviRetroApp {
    constructor() {
        this.config = new ConfigManager();
        this.auth = new AuthManager();
        this.ui = new UIManager();
        this.data = new DataManager(this.auth);
        this.schema = this.data.schema; // Use the schema manager from data manager
        this.commands = new CommandManager(this);
        this.updateUI = new UpdateUIManager();
        this.locationManager = new LocationManager();
        this.weather = new WeatherService(this.config, this.locationManager);

        // Initialize Justice commands
        this.initializeJusticeCommands();
        this.security = new SecurityHeadersManager();
        this.securityErrorHandler = new SecurityErrorHandler();
        this.securityMonitor = new SecurityMonitor();
        // Initialize core UI utilities first (needed by modules)
        this.uiUtilities = new UIUtilities();
        this.modalManagement = new ModalManagement(this.data, this.ui);
        this.adminManager = new AdminManager(this.data, this.ui, this.config);

        // Register modules with the registry
        this.registerModules();

        // Initialize modules will be called after auth is ready
        this.modulesInitialized = false;
        // Setup command execution wrapper
        this.setupCommandsExecute();

        // Vehicle management event handlers are set up by the modular system
        this.modalManagement.setIncidentStatusUpdateHandler((incidentId) => this.incidentManagement.updateIncidentStatus(incidentId));
        this.systemSettingsManager = new SystemSettingsManager(this.data, this.ui, this.auth, this.config, { systemSettingsTemplates, systemSettingsModals });
        this.googleMapsAddress = new GoogleMapsAddressSearch(this.config);
        this.migrationManager = null; // Will be initialized after auth
        this.featureManager = null; // Will be initialized after auth
        this.currentUser = null;
        this.currentTab = 'dashboard';
        this.currentScreen = null; // Track current screen for navigation
        this.selectedPerson = null; // Track selected person for detail view
        this.selectedProperty = null; // Track selected property
        this.selectedEncampment = null; // Track selected encampment for detail view

        // Make app instance globally available for commands
        window.app = this;

        // Narrative management will be made globally available after module initialization

        // Make medical toggle function globally available
        window.toggleMedicalDetails = function(toggleElement) {
            const detailsElement = toggleElement.parentElement.querySelector('.medical-details');
            const toggleIcon = toggleElement.querySelector('.toggle-icon');
            const isCollapsed = detailsElement.classList.contains('collapsed');
            
            if (isCollapsed) {
                detailsElement.classList.remove('collapsed');
                toggleElement.classList.remove('collapsed');
                toggleIcon.textContent = '▼';
            } else {
                detailsElement.classList.add('collapsed');
                toggleElement.classList.add('collapsed');
                toggleIcon.textContent = '▶';
            }
        };

        this.init();
    }

    /**
     * Register all modules with the module registry
     */
    registerModules() {
        // Register modules with proper dependencies
        moduleRegistry.register('PropertyManagement', PropertyManagement);
        moduleRegistry.register('BikeManagement', BikeManagement);
        moduleRegistry.register('AddressManagement', AddressManagement);
        moduleRegistry.register('EncampmentManagement', EncampmentManagement);
        moduleRegistry.register('IncidentManagement', IncidentManagement);
        moduleRegistry.register('VehicleManagement', VehicleManagement);
        moduleRegistry.register('PeopleManagement', PeopleManagement);
        moduleRegistry.register('ActivityManagement', ActivityManagement);
        moduleRegistry.register('AIModule', AIModule);
        moduleRegistry.register('OrganizationManagement', OrganizationManagement);
        moduleRegistry.register('OutreachManagement', OutreachManagement);
        moduleRegistry.register('ReportsManagement', ReportsManagement);
        moduleRegistry.register('SystemSettings', SystemSettings);
        moduleRegistry.register('NarrativeManagement', NarrativeManagement);
        moduleRegistry.register('JusticeModule', JusticeModule);
    }

    /**
     * Initialize all registered modules
     */
    async initializeModules() {
        const dependencies = {
            dataManager: this.data,
            authManager: this.auth,
            uiManager: this.ui,
            uiUtilities: this.uiUtilities,
            modalManagement: this.modalManagement
        };

        await moduleRegistry.initializeAll(dependencies);

        // Set module references for backward compatibility
        this.propertyManagement = moduleRegistry.get('PropertyManagement');
        this.bikeManagement = moduleRegistry.get('BikeManagement');
        this.addressManagement = moduleRegistry.get('AddressManagement');
        this.encampmentManagement = moduleRegistry.get('EncampmentManagement');
        this.incidentManagement = moduleRegistry.get('IncidentManagement');
        this.vehicleManagement = moduleRegistry.get('VehicleManagement');
        this.peopleManagement = moduleRegistry.get('PeopleManagement');
        this.activityManagement = moduleRegistry.get('ActivityManagement');
        this.aiModule = moduleRegistry.get('AIModule');
        this.organizationManagement = moduleRegistry.get('OrganizationManagement');
        this.outreachManagement = moduleRegistry.get('OutreachManagement');
        this.reportsManagement = moduleRegistry.get('ReportsManagement');
        this.systemSettings = moduleRegistry.get('SystemSettings');
        this.narrativeManagement = moduleRegistry.get('NarrativeManagement');
        this.justiceModule = moduleRegistry.get('JusticeModule');

        // Make narrative management globally available for backward compatibility
        if (this.narrativeManagement) {
            window.narrativeEditor = this.narrativeManagement;
            window.narrativeFormManager = this.narrativeManagement.getFormManager();
            window.narrativeRenderManager = this.narrativeManagement.getRenderManager();
        }
    }

    async initializeJusticeCommands() {
        try {
            const { JusticeCommands } = await import('../../modules/justice/js/justice-commands.js');
            const justiceCommands = new JusticeCommands();
            justiceCommands.register(this);
        } catch (error) {
            console.error('Failed to initialize Justice commands:', error);
        }
    }

    async init() {
        try {
            // Initialize security systems first
            this.security.initialize();
            this.securityErrorHandler.initialize();
            this.securityMonitor.startMonitoring();

            // Electron APIs are set up via preload script

            // Initialize Supabase connection early for login
            try {
                await this.auth.initializeSupabase();
            } catch (error) {
                console.error('Supabase initialization failed:', error.message);
                // Continue anyway - user will see error when trying to login
            }

            // Show login screen immediately
            await this.showLogin();
        } catch (error) {
            console.error('Initialization error:', error);
            // Show error screen
            document.body.innerHTML = `
                <div style="color: #ff0000; background: #000000; padding: 20px; font-family: monospace;">
                    <h1>S.T.E.V.I DOS - Initialization Error</h1>
                    <p>Error: ${error.message}</p>
                    <p>Stack: ${error.stack}</p>
                    <p>Please check the console for more details.</p>
                    <button onclick="location.reload()" style="background: #ff0000; color: #000000; padding: 10px; border: none; font-family: monospace;">Retry</button>
                </div>
            `;
        }
    }

    async postAuthBoot() {
        // Start boot sequence with post-auth loading
        // Show boot screen and initialize data concurrently
        const bootPromise = this.ui.showBootSequenceWithDataLoading();
        const dataPromise = this.initializeDataAfterAuth();

        // Wait for both to complete
        await Promise.all([bootPromise, dataPromise]);

        // After boot/data load, ensure dashboard weather renders
        try {
            // Delay a tick to allow DOM/template mount
            setTimeout(() => {
                if (document.getElementById('forecast-grid')) {
                    this.weather.renderDashboardWeather();
                }
            }, 200);
        } catch (e) {
            console.warn('Weather dashboard render init failed:', e);
        }
    }

    async initializeDataAfterAuth() {
        try {
            // SECURITY CHECK: Verify user has IHARC permissions
            const hasIharcAccess = await this.verifyIharcUserAccess();
            if (!hasIharcAccess) {
                console.error('Access denied: User does not have IHARC permissions');
                this.ui.showDialog('Access Denied', 
                    'This application is restricted to IHARC staff only.\nPlease contact your administrator for access.', 
                    'error');
                
                // Sign out the unauthorized user
                await this.auth.logout();
                this.showLogin();
                return;
            }

            // Initialize the data manager
            await this.data.initialize();
            await this.data.initializeAfterAuth();

            // After authentication is ready
            if (!this.modulesInitialized) {
                await this.initializeModules();
                this.modulesInitialized = true;
            }

            // Reinitialize services that depend on vault after authentication
            await this.reinitializeVaultDependentServices();
        } catch (error) {
            console.error('Error during post-auth data initialization:', error);
            throw error;
        }
    }

    /**
     * Reinitialize services that depend on the vault after authentication
     */
    async reinitializeVaultDependentServices() {
        try {
            console.log('🔄 Reinitializing vault-dependent services...');

            // Check if vault manager is available
            if (!this.config.vaultManager) {
                console.warn('VaultManager not available, skipping service reinitialization');
                return;
            }

            // Test vault connectivity
            try {
                console.log('🔄 Testing vault connectivity...');
                const testKey = await this.config.vaultManager.getSecret('google_api_key');
                console.log('🔄 Vault test result:', testKey ? 'Connected' : 'No keys found');
            } catch (error) {
                console.warn('🔄 Vault connectivity test failed:', error.message);
            }

            // Reinitialize weather service to try getting API key again
            if (this.weather) {
                console.log('🔄 Reinitializing weather service...');
                this.weather.apiKey = null; // Clear cached null value
                this.weather.clearCache(); // Clear weather cache

                // If we're on the dashboard, refresh the weather widget
                const currentTab = document.querySelector('.tab-button.active')?.dataset?.tab;
                if (currentTab === 'dashboard') {
                    setTimeout(() => {
                        this.initializeWeatherWidget();
                    }, 1000);
                }
            }

            // Reinitialize admin manager's Gemini service
            if (this.adminManager && this.adminManager.geminiService) {
                console.log('🔄 Reinitializing Gemini service...');
                this.adminManager.geminiService.apiKey = null; // Clear cached null value
            }

            console.log('🔄 Vault-dependent services reinitialization completed');
        } catch (error) {
            console.error('Error reinitializing vault-dependent services:', error);
        }
    }

    /**
     * Add execute method to commands for template compatibility
     */
    setupCommandsExecute() {
        this.commands.execute = async (commandName, ...args) => {
            return await this.commands.executeCommand(commandName, args);
        };
    }

    /**
     * Verify that the current user has IHARC permissions
     * Uses the same logic as the is_iharc_user() database function
     */
    async verifyIharcUserAccess() {
        try {
            const currentUser = this.auth.getCurrentUser();
            if (!currentUser) {
                console.warn('No authenticated user found');
                return false;
            }

            // Try to call the is_iharc_user() function directly via RPC
            const supabase = await this.data.getSupabaseClient();
            if (supabase) {
                const { data, error } = await supabase.rpc('is_iharc_user');
                
                if (!error && data === true) {
                    console.log('✅ User verified as IHARC user via is_iharc_user() RPC');
                    return true;
                }
                
                if (error) {
                    console.warn('is_iharc_user() RPC failed:', error.message);
                }
            }

            // Fallback: Check user roles in database
            console.log('Checking IHARC roles in database...');
            const hasRole = await this.checkIharcRoleInDatabase(currentUser.id);
            if (hasRole) {
                console.log('✅ User verified as IHARC user via database role check');
                return true;
            }

            console.log('❌ User does not have IHARC permissions');
            return false;

        } catch (error) {
            console.error('Error verifying IHARC user access:', error);
            return false;
        }
    }

    /**
     * Check if user has IHARC role in the database
     */
    async checkIharcRoleInDatabase(userId) {
        try {
            const query = `
                SELECT 1 
                FROM core.user_roles ur
                JOIN core.roles r ON ur.role_id = r.id
                WHERE ur.user_id = $1 
                AND r.name IN ('iharc_staff', 'iharc_admin', 'iharc_supervisor', 'iharc_volunteer')
                LIMIT 1
            `;

            const result = await this.data.query(query, [userId]);
            return result && result.length > 0;

        } catch (error) {
            console.error('Error checking IHARC role in database:', error);
            return false;
        }
    }

    async showLogin() {
        this.ui.showScreen('login');

        // Always require fresh login - no auto-login
        console.log('🔐 Showing login screen - fresh authentication required');

        // Set up login form handlers
        const emailInput = document.getElementById('login-email');
        const passwordInput = document.getElementById('login-password');
        const submitButton = document.getElementById('login-submit');
        const statusDiv = document.getElementById('login-status');

        // Clear any existing event listeners by cloning and replacing elements
        if (submitButton) {
            const newSubmitButton = submitButton.cloneNode(true);
            submitButton.parentNode.replaceChild(newSubmitButton, submitButton);
        }
        if (emailInput) {
            const newEmailInput = emailInput.cloneNode(true);
            emailInput.parentNode.replaceChild(newEmailInput, emailInput);
        }
        if (passwordInput) {
            const newPasswordInput = passwordInput.cloneNode(true);
            passwordInput.parentNode.replaceChild(newPasswordInput, passwordInput);
        }

        // Get the new elements after replacement
        const cleanEmailInput = document.getElementById('login-email');
        const cleanPasswordInput = document.getElementById('login-password');
        const cleanSubmitButton = document.getElementById('login-submit');

        // Reset form state
        if (cleanSubmitButton) {
            cleanSubmitButton.disabled = false;
            cleanSubmitButton.textContent = 'LOGIN';
        }
        if (statusDiv) {
            statusDiv.textContent = '';
            statusDiv.className = 'login-status';
        }

        // Load saved username if available
        const savedUsername = localStorage.getItem('stevidos_saved_username');
        const saveUsernameCheckbox = document.getElementById('save-username');

        if (savedUsername && cleanEmailInput) {
            cleanEmailInput.value = savedUsername;
            if (saveUsernameCheckbox) saveUsernameCheckbox.checked = true;
            if (cleanPasswordInput) cleanPasswordInput.focus(); // Focus password if username is saved
        } else {
            if (cleanEmailInput) cleanEmailInput.focus(); // Focus email if no saved username
        }

        // Handle form submission
        const handleLogin = async (e) => {
            e.preventDefault();

            const email = cleanEmailInput?.value.trim() || '';
            const password = cleanPasswordInput?.value || '';

            if (!email || !password) {
                this.ui.showStatus('Please enter both email and password', 'error');
                return;
            }

            if (cleanSubmitButton) {
                cleanSubmitButton.disabled = true;
                cleanSubmitButton.textContent = 'AUTHENTICATING...';
            }
            if (statusDiv) {
                statusDiv.textContent = 'Connecting to IHARC systems...';
            }

            try {
                console.log('🔄 Attempting login with email:', email);
                const result = await this.auth.login(email, password);
                console.log('🔄 Login result:', result);

                if (result.success) {
                    // Handle username saving
                    if (saveUsernameCheckbox.checked) {
                        localStorage.setItem('stevidos_saved_username', email);
                    } else {
                        localStorage.removeItem('stevidos_saved_username');
                    }

                    this.currentUser = result.user;
                    if (statusDiv) {
                        statusDiv.textContent = 'Authentication successful. Loading interface...';
                    }

                    // Small delay for user feedback
                    setTimeout(async () => {
                        console.log('🔄 Login successful, starting post-auth boot sequence...');
                        try {
                            await this.postAuthBoot();
                            console.log('🔄 Post-auth boot completed, showing main interface...');
                            await this.showMainInterface();
                            console.log('🔄 showMainInterface completed successfully');
                        } catch (error) {
                            console.error('❌ Error in post-auth flow:', error);
                        }
                    }, 1000);
                } else {
                    if (statusDiv) {
                        statusDiv.textContent = result.error || 'Authentication failed';
                    }
                    if (cleanSubmitButton) {
                        cleanSubmitButton.disabled = false;
                        cleanSubmitButton.textContent = 'LOGIN';
                    }
                }
            } catch (error) {
                console.error('Login error:', error);
                if (statusDiv) {
                    statusDiv.textContent = 'Connection error. Please try again.';
                }
                if (cleanSubmitButton) {
                    cleanSubmitButton.disabled = false;
                    cleanSubmitButton.textContent = 'LOGIN';
                }
            }
        };

        // Event listeners
        if (cleanSubmitButton) {
            cleanSubmitButton.addEventListener('click', handleLogin);
        }

        // Enter key handling
        const handleKeyPress = (e) => {
            if (e.key === 'Enter') {
                if (e.target === cleanEmailInput && cleanPasswordInput) {
                    cleanPasswordInput.focus();
                } else if (e.target === cleanPasswordInput) {
                    handleLogin(e);
                }
            }
        };

        if (cleanEmailInput) {
            cleanEmailInput.addEventListener('keypress', handleKeyPress);
        }
        if (cleanPasswordInput) {
            cleanPasswordInput.addEventListener('keypress', handleKeyPress);
        }
    }

    async showMainInterface() {
        console.log('🔄 Starting showMainInterface...');
        this.ui.showScreen('main');

        // Update user info
        const userInfoElement = document.getElementById('user-info');
        if (this.currentUser) {
            userInfoElement.textContent = `Logged in as: ${this.currentUser.email}`;
        }

        // Start datetime updates
        this.uiUtilities.updateDateTime();
        setInterval(() => this.uiUtilities.updateDateTime(), 1000);

        // Set up network status monitoring
        this.uiUtilities.networkMonitor.setAuthManager(this.auth);
        this.uiUtilities.setupNetworkMonitoring();

        // Set up tab navigation
        this.setupTabNavigation();

        // Show/hide admin tab based on user role
        this.setupRoleBasedVisibility();

        console.log('🔄 Skipping migration system (removed for simplicity)');

        // Initialize real schemas from database now that user is authenticated
        console.log('🔄 Initializing real schemas after authentication...');
        await this.data.initializeRealSchemas();
        console.log('🔄 Real schemas initialization completed');

        // Ensure encampments feature is available
        await this.ensureEncampmentsFeature();

        // Load initial content
        await this.loadTabContent(this.currentTab);

        // Set up menu item click handlers
        this.setupMenuHandlers();

        // Set up real-time data change listeners
        this.setupDataChangeListeners();

        // Check for updates on startup (non-blocking)
        this.checkForUpdatesOnStartup();
    }

    // Migration system removed for simplicity - no longer needed

    async showMigrationDialog() {
        return await this.modalManagement.showMigrationDialog();
    }

    // Feature management removed for simplicity - all features enabled by default

    async ensureEncampmentsFeature() {
        try {
            console.log('🏕️ Ensuring encampments feature is available...');

            // Initialize feature manager if not already done
            if (!this.featureManager) {
                const { FeatureManager } = await import('./feature-manager.js');
                this.featureManager = new FeatureManager(this.data, this.migrationManager);
            }

            // Check if encampments feature is available
            await this.featureManager.checkFeatureAvailability();

            if (this.featureManager.isFeatureEnabled('encampments')) {
                console.log('✅ Encampments feature is available');
            } else {
                console.log('⚠️ Encampments feature is not available, attempting to enable...');

                // Try to create the encampments table if it doesn't exist
                if (this.migrationManager) {
                    try {
                        const migrationResult = await this.migrationManager.migrateToEncampments();
                        if (migrationResult) {
                            console.log('✅ Encampments table created successfully');
                            // Re-check feature availability
                            await this.featureManager.checkFeatureAvailability();
                            if (this.featureManager.isFeatureEnabled('encampments')) {
                                console.log('✅ Encampments feature is now available');
                            }
                        }
                    } catch (error) {
                        console.error('❌ Failed to enable encampments feature:', error);
                    }
                }
            }
        } catch (error) {
            console.error('❌ Error ensuring encampments feature:', error);
        }
    }

    getFeatureUnavailableContent(featureName) {
        const featureInfo = this.featureManager ? this.featureManager.getFeatureRequirements(featureName) : null;

        return `
            <div class="content-section">
                <div class="feature-unavailable">
                    <div class="feature-unavailable-icon">🚫</div>
                    <h2>Feature Not Available</h2>
                    <p>The <strong>${featureName}</strong> feature is not available in your current setup.</p>

                    ${featureInfo ? `
                        <div class="feature-requirements">
                            <h3>Requirements:</h3>
                            <ul>
                                <li>Minimum version: ${featureInfo.minVersion}</li>
                                ${featureInfo.tables ? `<li>Required tables: ${featureInfo.tables.join(', ')}</li>` : ''}
                            </ul>
                        </div>
                    ` : ''}

                    <div class="feature-actions">
                        <button class="primary-button" onclick="app.checkForUpdatesOnStartup()">
                            Check for Updates
                        </button>
                        <button class="secondary-button" onclick="app.loadTabContent('dashboard')">
                            Go to Dashboard
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    // Test function for validating update process
    async testUpdateProcess() {
        console.log('🧪 Starting update process validation...');

        try {
            if (!this.migrationManager || !this.featureManager) {
                console.error('❌ Migration or feature manager not initialized');
                return false;
            }

            // Test 1: Check current schema version
            const currentVersion = await this.migrationManager.getCurrentSchemaVersion();
            console.log(`📊 Current schema version: ${currentVersion}`);

            // Test 2: Check feature availability
            await this.featureManager.checkFeatureAvailability();
            const features = this.featureManager.getAvailableFeatures();
            console.log(`📊 Available features: ${features.join(', ')}`);

            // Test 3: Validate current setup
            const validation = await this.featureManager.validateCurrentSetup();
            console.log('📊 Setup validation:', validation);

            // Test 4: Check if migrations are needed
            const migrationsNeeded = await this.migrationManager.checkMigrationsNeeded();
            console.log(`📊 Migrations needed: ${migrationsNeeded}`);

            // Test 5: Validate schema consistency
            const schemaValidation = await this.migrationManager.validateSchema();
            console.log('📊 Schema validation:', schemaValidation);

            // Test 6: Test encampments functionality if available
            if (this.featureManager.isFeatureEnabled('encampments')) {
                console.log('✅ Encampments feature is available');

                // Test basic encampment operations
                try {
                    const testEncampment = {
                        name: 'Test Encampment',
                        location: 'Test Location',
                        status: 'active',
                        coordinates: '43.9589, -78.1648'
                    };

                    // Note: This is just a validation test, not actually creating data
                    const validationResult = this.encampmentManager.validateEncampmentData(testEncampment);
                    console.log('📊 Encampment validation test:', validationResult);
                } catch (error) {
                    console.warn('⚠️ Encampment validation test failed:', error);
                }
            } else {
                console.log('❌ Encampments feature is not available');
            }

            // Summary
            const summary = {
                schemaVersion: currentVersion,
                featuresAvailable: features,
                migrationsNeeded,
                validationPassed: validation.valid,
                encampmentsEnabled: this.featureManager.isFeatureEnabled('encampments')
            };

            console.log('📊 Update process validation summary:', summary);

            this.ui.showDialog(
                'Update Process Validation',
                `
                <div class="validation-results">
                    <h3>Validation Results</h3>
                    <p><strong>Schema Version:</strong> ${currentVersion}</p>
                    <p><strong>Available Features:</strong> ${features.join(', ') || 'None'}</p>
                    <p><strong>Migrations Needed:</strong> ${migrationsNeeded ? 'Yes' : 'No'}</p>
                    <p><strong>Validation Status:</strong> ${validation.valid ? '✅ Passed' : '❌ Failed'}</p>
                    <p><strong>Encampments Enabled:</strong> ${this.featureManager.isFeatureEnabled('encampments') ? '✅ Yes' : '❌ No'}</p>

                    ${validation.issues.length > 0 ? `
                        <div class="validation-issues">
                            <h4>Issues:</h4>
                            <ul>
                                ${validation.issues.map(issue => `<li>${issue}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${validation.warnings.length > 0 ? `
                        <div class="validation-warnings">
                            <h4>Warnings:</h4>
                            <ul>
                                ${validation.warnings.map(warning => `<li>${warning}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
                `,
                'info',
                true
            );

            return validation.valid;

        } catch (error) {
            console.error('❌ Update process validation failed:', error);
            this.ui.showDialog(
                'Validation Error',
                `Update process validation failed: ${error.message}`,
                'error'
            );
            return false;
        }
    }

    setupTabNavigation() {
        const tabs = document.querySelectorAll('.tab');

        tabs.forEach(tab => {
            tab.addEventListener('click', async () => {
                const tabName = tab.dataset.tab;

                if (tabName === 'logout') {
                    await this.logout();
                    return;
                }

                // Update active tab
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                this.currentTab = tabName;
                await this.loadTabContent(tabName);
            });
        });

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'F10') {
                e.preventDefault();
                // Show menu or help
            } else if (e.key === 'Escape') {
                e.preventDefault();
                // Handle escape
            } else if (e.key === 'F1') {
                e.preventDefault();
                // Show help
            }
        });

        // Set up data change listeners for auto-refresh
        this.setupDataChangeListeners();
    }

    setupDataChangeListeners() {
        // Listen for data changes and refresh UI accordingly
        // Removed duplicate dataChange listener - using the enhanced one in setupDataChangeListeners()
    }

    setupRoleBasedVisibility() {
        // Show/hide admin tab based on user role
        const adminTab = document.querySelector('.admin-tab');
        if (adminTab) {
            if (this.auth.isAdmin()) {
                adminTab.style.display = 'block';
            } else {
                adminTab.style.display = 'none';
            }
        }
    }

    async loadTabContent(tabName, screenName = null) {
        const contentArea = document.getElementById('content-area');

        if (!contentArea) {
            console.error('Content area element not found');
            this.ui.showDialog('Error', 'Content area not found. Please refresh the page.', 'error');
            return;
        }

        this.ui.setStatus('Loading...');

        try {
            // Remove any existing content classes
            contentArea.classList.remove('people-management-content', 'addresses-management-content', 'incidents-content', 'incident-search-content');

            // Handle sub-screens for records tab
            if (tabName === 'records' && screenName) {
                switch (screenName) {
                    case 'people-management':
                        contentArea.innerHTML = await this.peopleManagement.loadPeopleManagementContent();
                        contentArea.classList.add('people-management-content');
                        this.currentScreen = 'people-management';
                        break;
                    case 'addresses-management':
                        await this.loadAddressesManagementContent();
                        contentArea.classList.add('addresses-management-content');
                        this.currentScreen = 'addresses-management';
                        break;
                    case 'organizations-management':
                        contentArea.innerHTML = await this.loadOrganizationsManagementContent();
                        contentArea.classList.add('organizations-management-content');
                        this.currentScreen = 'organizations-management';
                        break;
                    case 'bikes-management':
                        contentArea.innerHTML = await this.loadBikesManagementContent();
                        contentArea.classList.add('bikes-management-content');
                        this.currentScreen = 'bikes-management';
                        break;
                    case 'person-detail':
                        if (this.selectedPerson) {
                            contentArea.innerHTML = await this.loadPersonDetailContent(this.selectedPerson.id);
                            contentArea.classList.add('people-management-content');
                            this.currentScreen = 'person-detail';
                        }
                        break;
                    case 'vehicle-detail':
                        if (this.selectedVehicle && this.vehicleManagement?.detailManager) {
                            contentArea.innerHTML = await this.vehicleManagement.detailManager.getVehicleDetailContent(this.selectedVehicle.id);
                            contentArea.classList.add('vehicle-management-content');
                            this.currentScreen = 'vehicle-detail';
                            
                            // Set up vehicle detail tabs after content is loaded
                            setTimeout(() => {
                                this.vehicleManagement.detailManager.setupVehicleDetailTabs();
                                this.vehicleManagement.detailManager.loadVehicleActivities(this.selectedVehicle.id);
                            }, 100);
                        }
                        break;
                    case 'incident-search':
                        contentArea.innerHTML = await this.incidentManagement.loadIncidentSearchContent();
                        contentArea.classList.add('incident-search-content');
                        this.currentScreen = 'incident-search';
                        this.incidentManagement.setupIncidentSearch();
                        break;
                    default:
                        contentArea.innerHTML = await this.loadRecordsContent();
                        this.currentScreen = null;
                }
            } else if (tabName === 'property' && screenName) {
                // Handle sub-screens for property tab
                switch (screenName) {
                    case 'found-property-list':
                        contentArea.innerHTML = await this.loadFoundPropertyListContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'found-property-list';
                        break;
                    case 'missing-property-list':
                        contentArea.innerHTML = await this.loadMissingPropertyListContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'missing-property-list';
                        break;
                    case 'log-property-recovery':
                        contentArea.innerHTML = await this.loadLogPropertyRecoveryContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'log-property-recovery';
                        this.setupPropertyRecoveryForm();
                        break;
                    case 'create-missing-report':
                        contentArea.innerHTML = await this.loadCreateMissingReportContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-report';
                        break;
                    case 'create-missing-general':
                        contentArea.innerHTML = await this.loadCreateMissingGeneralContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-general';
                        this.setupMissingGeneralForm();
                        break;
                    case 'create-missing-bike':
                        contentArea.innerHTML = await this.loadCreateMissingBikeContent();
                        contentArea.classList.add('property-management-content');
                        this.currentScreen = 'create-missing-bike';
                        this.setupMissingBikeForm();
                        break;
                    default:
                        contentArea.innerHTML = await this.loadPropertyContent();
                        break;
                }
            } else if (tabName === 'encampments' && screenName) {
                // Handle sub-screens for encampments tab
                switch (screenName) {
                    case 'add-encampment':
                        contentArea.innerHTML = await this.loadAddEncampmentContent();
                        contentArea.classList.add('encampments-management-content');
                        this.currentScreen = 'add-encampment';
                        this.setupAddEncampmentForm();
                        break;
                    case 'encampment-detail':
                        if (this.selectedEncampment) {
                            contentArea.innerHTML = await this.loadEncampmentDetailContent(this.selectedEncampment.id);
                            contentArea.classList.add('encampments-management-content');
                            this.currentScreen = 'encampment-detail';
                        }
                        break;
                    default:
                        contentArea.innerHTML = await this.loadEncampmentsContent();
                        this.currentScreen = null;
                        this.currentScreen = null;
                }
            } else if (tabName === 'incidents' && screenName) {
                // Handle sub-screens for incidents tab
                switch (screenName) {
                    case 'create-comprehensive-incident':
                        contentArea.innerHTML = await this.incidentManagement.createIncidentWorkflow();
                        contentArea.classList.add('incident-creation-content');
                        this.currentScreen = 'create-comprehensive-incident';
                        // Initialize form after DOM insertion
                        setTimeout(async () => {
                            await this.incidentManagement.initializeIncidentCreationForm();
                        }, 100);
                        break;
                    // edit-incident case removed - now handled entirely by incident management module
                    default:
                        contentArea.innerHTML = await this.incidentManagement.loadIncidentsContent();
                        this.currentScreen = null;
                }
            } else {
                // Handle main tabs
                switch (tabName) {
                    case 'dashboard':
                        contentArea.innerHTML = await this.loadDashboardContent();
                        // Initialize dashboard components after DOM is ready
                        setTimeout(() => {
                            this.initializeDashboard();
                        }, 100);
                        break;
                    case 'incidents':
                        contentArea.innerHTML = await this.incidentManagement.loadIncidentsContent();
                        contentArea.classList.add('incidents-content');
                        // Initialize incidents after DOM is ready
                        setTimeout(() => {
                            this.incidentManagement.initializeUnifiedIncidents();
                        }, 100);
                        break;
                    case 'records':
                        contentArea.innerHTML = await this.loadRecordsContent();
                        this.currentScreen = null;
                        break;
                    case 'property':
                        contentArea.innerHTML = await this.loadPropertyContent();
                        break;
                    case 'encampments':
                        if (this.featureManager && this.featureManager.isFeatureEnabled('encampments')) {
                            await this.encampmentManagement.showEncampmentList();
                        } else {
                            // Try to enable the feature first
                            console.log('🔧 Attempting to enable encampments feature...');
                            await this.ensureEncampmentsFeature();

                            // Check again after attempting to enable
                            if (this.featureManager && this.featureManager.isFeatureEnabled('encampments')) {
                                await this.encampmentManagement.showEncampmentList();
                            } else {
                                // As a last resort, try to load the content anyway (for testing)
                                console.log('⚠️ Loading encampments content despite feature check failure');
                                try {
                                    await this.encampmentManagement.showEncampmentList();
                                } catch (error) {
                                    console.error('❌ Failed to load encampments content:', error);
                                    contentArea.innerHTML = this.getFeatureUnavailableContent('encampments');
                                }
                            }
                        }
                        break;
                    case 'outreach':
                        contentArea.innerHTML = await this.loadOutreachContent();
                        contentArea.classList.add('outreach-content');
                        // Initialize outreach after DOM is ready
                        setTimeout(() => {
                            this.initializeOutreach();
                        }, 100);
                        break;
                    case 'reports':
                        contentArea.innerHTML = await this.loadReportsContent();
                        break;
                    case 'system':
                        contentArea.innerHTML = await this.loadSystemContent();
                        break;
                    case 'admin':
                        if (this.auth.isAdmin()) {
                            contentArea.innerHTML = await this.loadAdminContent();
                        } else {
                            contentArea.innerHTML = '<div class="error">Access denied. Admin privileges required.</div>';
                        }
                        break;
                    default:
                        contentArea.innerHTML = '<div>Content not available</div>';
                }
            }
            
            this.ui.setStatus('Ready');

            // Cleanup previous tab if needed
            this.cleanupPreviousTab();

            // Dashboard initialization is handled by loadDashboardContent()

            // Initialize unified incidents interface is handled by the setTimeout above

            // Property tab now uses menu-based navigation, no initialization needed

            // Initialize admin if it's the admin tab and user has admin access
            if (tabName === 'admin') {
                console.log('=== ADMIN TAB ACCESS CHECK ===');
                console.log('Current user:', this.auth.currentUser?.email);
                console.log('User app_metadata:', this.auth.currentUser?.app_metadata);
                
                // Debug user permissions first
                await this.auth.debugUserPermissions();
                
                // Check if user has admin access
                const hasAdminPermission = this.auth.hasPermission('admin.access');
                const isAdmin = this.auth.isAdmin();
                
                console.log('hasPermission("admin.access"):', hasAdminPermission);
                console.log('isAdmin():', isAdmin);
                
                if (hasAdminPermission || isAdmin) {
                    console.log('User has admin access, initializing admin...');
                    this.initializeAdmin();
                } else {
                    console.warn('User does not have admin access');
                    console.log('User roles:', this.auth.getUserRoles());
                    console.log('User permissions:', this.auth.getUserPermissions());
                    contentArea.innerHTML = `
                        <div class="error">
                            <h3>Access Denied</h3>
                            <p>Admin privileges required.</p>
                            <p>Current user: ${this.auth.currentUser?.email}</p>
                            <p>User roles: ${this.auth.getUserRoles().join(', ') || 'None'}</p>
                            <p>User permissions: ${this.auth.getUserPermissions().join(', ') || 'None'}</p>
                            <button onclick="app.auth.refreshUserPermissions()" class="primary-button">Refresh Permissions</button>
                        </div>
                    `;
                }
            }
        } catch (error) {
            console.error('Error loading content:', error);
            contentArea.innerHTML = '<div>Error loading content</div>';
            this.ui.setStatus('Error');
        }
    }

    cleanupPreviousTab() {
        // Cleanup old dispatch-specific resources (legacy)
        if (this.dispatchUpdateInterval) {
            clearInterval(this.dispatchUpdateInterval);
            this.dispatchUpdateInterval = null;
        }

        if (this.dispatchKeyHandler) {
            document.removeEventListener('keydown', this.dispatchKeyHandler);
            this.dispatchKeyHandler = null;
        }

        if (this.dispatchDataChangeHandler) {
            window.removeEventListener('dataChange', this.dispatchDataChangeHandler);
            this.dispatchDataChangeHandler = null;
        }

        // Cleanup unified incidents resources
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
            this.incidentsUpdateInterval = null;
        }

        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
            this.incidentsDataChangeHandler = null;
        }
    }

    // Comprehensive cleanup method for app shutdown
    async cleanup() {
        console.log('🧹 Starting SteviRetroApp cleanup...');

        try {
            // 1. Cleanup all tab-specific resources
            this.cleanupPreviousTab();

            // 2. Clear outreach update interval
            if (this.outreachUpdateInterval) {
                clearInterval(this.outreachUpdateInterval);
                this.outreachUpdateInterval = null;
            }

            // 3. Cleanup all event handlers
            if (this.outreachEventHandlers && Array.isArray(this.outreachEventHandlers)) {
                this.outreachEventHandlers.forEach(handler => {
                    if (handler.element && handler.event && handler.function) {
                        handler.element.removeEventListener(handler.event, handler.function);
                    }
                });
                this.outreachEventHandlers = [];
            }

            // 4. Cleanup security monitoring
            if (this.securityMonitor) {
                try {
                    this.securityMonitor.stopMonitoring();
                } catch (error) {
                    console.error('Error stopping security monitor:', error.message);
                }
            }

            // 5. Cleanup location manager
            if (this.locationManager) {
                try {
                    // If location manager has cleanup methods, call them
                    if (typeof this.locationManager.cleanup === 'function') {
                        await this.locationManager.cleanup();
                    }
                } catch (error) {
                    console.error('Error cleaning up location manager:', error.message);
                }
            }

            // 6. Cleanup weather service
            if (this.weather) {
                try {
                    // If weather service has cleanup methods, call them
                    if (typeof this.weather.cleanup === 'function') {
                        await this.weather.cleanup();
                    }
                } catch (error) {
                    console.error('Error cleaning up weather service:', error.message);
                }
            }

            // 7. Cleanup management modules
            const managementModules = [
                'propertyManager', 'bikeManager', 'encampmentManager',
                'addressManager', 'reportsManager', 'adminManager',
                'incidentManagement', 'vehicleManagement', 'peopleManagement',
                'systemSettingsManager', 'narrativeManagement'
            ];

            for (const moduleName of managementModules) {
                if (this[moduleName]) {
                    try {
                        if (typeof this[moduleName].cleanup === 'function') {
                            await this[moduleName].cleanup();
                        }
                    } catch (error) {
                        console.error(`Error cleaning up ${moduleName}:`, error.message);
                    }
                }
            }

            // 8. Clear any remaining intervals or timeouts
            // Note: We can't clear all intervals globally as that might affect other parts of the system
            // Instead, we rely on each module to clean up its own intervals

            console.log('✅ SteviRetroApp cleanup completed');
        } catch (error) {
            console.error('❌ Error during SteviRetroApp cleanup:', error.message);
        }
    }

    // Dashboard methods moved to dashboard module - delegate to dashboardModule
    async loadDashboardContent() {
        if (this.dashboardModule) {
            return await this.dashboardModule.loadDashboardContent();
        }
        return '<div class="error">Dashboard module not available</div>';
    }

    async initializeDashboard() {
        if (this.dashboardModule) {
            return await this.dashboardModule.initializeDashboard();
        }
    }

    async initializeWeatherWidget() {
        if (this.dashboardModule) {
            return await this.dashboardModule.initializeWeatherWidget();
        }
    }

    createWeatherDisplay(formattedData, rawData) {
        if (this.dashboardModule) {
            return this.dashboardModule.createWeatherDisplay(formattedData, rawData);
        }
        return '<div class="error">Dashboard module not available</div>';
    }

    async loadDashboardIncidents() {
        if (this.dashboardModule) {
            return await this.dashboardModule.loadDashboardIncidents();
        }
    }

    async loadDashboardStats() {
        if (this.dashboardModule) {
            return await this.dashboardModule.loadDashboardStats();
        }
    }

    // Outreach methods moved to outreach management module - delegate to outreachManagement
    async loadOutreachContent() {
        if (this.outreachManagement) {
            return await this.outreachManagement.loadOutreachContent();
        }
        return '<div class="error">Outreach management module not available</div>';
    }

    async initializeOutreach() {
        if (this.outreachManagement) {
            return await this.outreachManagement.initializeOutreach();
        }
    }

    setupIncidentsViewSwitching() {
        // Delegate to incident list manager
        if (this.incidentManagement && this.incidentManagement.listManager) {
            this.incidentManagement.listManager.setupIncidentsViewSwitching();
        } else {
            console.warn('Incident management not available for view switching setup');
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (this.currentTab !== 'incidents') return;

            switch(e.key) {
                case 'F1':
                    e.preventDefault();
                    document.querySelector('[data-view="dispatch"]')?.click();
                    break;
                case 'F2':
                    e.preventDefault();
                    document.querySelector('[data-view="list"]')?.click();
                    break;
                case 'F3':
                    e.preventDefault();
                    document.querySelector('[data-view="search"]')?.click();
                    break;
                case 'F4':
                    e.preventDefault();
                    document.querySelector('[data-action="create-incident-screen"]')?.click();
                    break;
                case 'F5':
                    e.preventDefault();
                    document.getElementById('refresh-incidents')?.click();
                    break;
                case 'Escape':
                    // Clear search or go back to dispatch view
                    if (this.currentIncidentsView === 'search') {
                        const searchInput = document.getElementById('search-input');
                        if (searchInput && searchInput.value) {
                            searchInput.value = '';
                            document.getElementById('clear-search')?.click();
                        } else {
                            document.querySelector('[data-view="dispatch"]')?.click();
                        }
                    }
                    break;
                case 'Enter':
                    // Quick search when in search view
                    if (this.currentIncidentsView === 'search' && e.target.id === 'search-input') {
                        e.preventDefault();
                        document.getElementById('search-btn')?.click();
                    }
                    break;
            }
        });
    }

    setupUnifiedIncidentsEventHandlers() {
        // Delegate to incident list manager
        if (this.incidentManagement && this.incidentManagement.listManager) {
            this.incidentManagement.listManager.setupUnifiedIncidentsEventHandlers();
        } else {
            console.warn('Incident management not available for event handlers setup');
        }

        // Filter functionality
        const statusFilter = document.getElementById('status-filter');
        const priorityFilter = document.getElementById('priority-filter');

        if (statusFilter) {
            statusFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        if (priorityFilter) {
            priorityFilter.addEventListener('change', () => {
                this.loadIncidentsData();
            });
        }

        // Listen for data changes to automatically refresh
        const dataChangeHandler = (event) => {
            const { table, operation, record } = event.detail;
            if (table === 'incidents' && this.currentTab === 'incidents') {
                // Only refresh for actual data changes, not cache refreshes
                if (operation !== 'refresh') {
                    console.log(`🔄 Incident ${operation} detected, refreshing incidents...`);
                    
                    // Check if we're currently viewing incident details and this is an update to the current incident
                    if (this.selectedIncident && operation === 'update' && record && record.id === this.selectedIncident.id) {
                        console.log('🎯 Updating current incident details without resetting view...');
                        
                        // Store current tab state - use the new tab system
                        const activeTab = document.querySelector('.incident-detail-tab.active');
                        const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : null;
                        
                        // Update the selected incident data
                        this.selectedIncident = record;
                        
                        // Delegate incident detail population to incident management module
                        if (this.incidentManagement && this.incidentManagement.detailManager) {
                            this.incidentManagement.detailManager.populateIncidentTabContent(record);
                        } else {
                            console.warn('Incident management not available for tab content population');
                        }
                        
                        // Restore the active tab if it was stored
                        if (activeTabId) {
                            setTimeout(() => {
                                // Use the current tab system to restore active state
                                const tabToActivate = document.querySelector(`.incident-detail-tab[data-tab="${activeTabId}"]`);
                                const paneToActivate = document.getElementById(`${activeTabId}-pane`);
                                
                                if (tabToActivate && paneToActivate) {
                                    // Remove active from all tabs and panes
                                    document.querySelectorAll('.incident-detail-tab').forEach(t => t.classList.remove('active'));
                                    document.querySelectorAll('.tab-pane').forEach(p => p.classList.remove('active'));
                                    
                                    // Activate the correct tab and pane
                                    tabToActivate.classList.add('active');
                                    paneToActivate.classList.add('active');
                                    
                                    // Re-initialize narrative editor if on narrative tab
                                    if (activeTabId === 'narrative') {
                                        setTimeout(() => {
                                            this.initializeNarrativeEditor(record);
                                        }, 50);
                                    }
                                }
                            }, 50);
                        }
                    } else {
                        // For other operations (create, delete) or when not viewing details, do full refresh
                        this.loadIncidentsData();
                    }
                }
            }
        };

        // Remove existing listener if any
        if (this.incidentsDataChangeHandler) {
            window.removeEventListener('dataChange', this.incidentsDataChangeHandler);
        }

        // Add new listener and store reference for cleanup
        this.incidentsDataChangeHandler = dataChangeHandler;
        window.addEventListener('dataChange', dataChangeHandler);
    }

    // setupIncidentFormFunctionality() method removed - now handled by incident-form-manager.js



    verifyEventHandlers() {
        // Verify that action buttons are properly accessible
        const actionButtons = document.querySelectorAll('[data-action]');
        console.log(`Found ${actionButtons.length} action buttons`);
        
        // Specifically check for create incident button
        const createButton = document.querySelector('[data-action="create-incident-screen"]');
        if (createButton) {
            console.log('Create incident button found and accessible');
        } else {
            console.warn('Create incident button not found - this may cause issues');
        }
        
        // Test if click handlers are working by adding a temporary test
        actionButtons.forEach(button => {
            if (!button.dataset.testHandler) {
                button.dataset.testHandler = 'true';
                // The global event delegation should handle this
            }
        });
    }

    // loadDispatchView method removed - now handled by incident list manager

    async loadListView() {
        const incidentsTable = document.getElementById('incidents-table');
        if (!incidentsTable) return;

        try {
            // Show loading state
            incidentsTable.innerHTML = '<div class="loading">Loading incidents...</div>';

            // Get all incidents
            let incidents = await this.data.search('incidents', {});

            // Apply filters
            const statusFilter = document.getElementById('status-filter')?.value;
            const priorityFilter = document.getElementById('priority-filter')?.value;

            if (statusFilter) {
                incidents = incidents.filter(incident => incident.status === statusFilter);
            }

            if (priorityFilter) {
                incidents = incidents.filter(incident => incident.priority === priorityFilter);
            }

            // Sort by creation date (newest first)
            incidents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            if (incidents.length === 0) {
                incidentsTable.innerHTML = '<div class="no-incidents">No incidents found</div>';
                return;
            }

            // Generate table HTML
            const tableHTML = `
                <table class="incidents-data-table">
                    <thead>
                        <tr>
                            <th>Number</th>
                            <th>Type</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Location</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${incidents.map(incident => `
                            <tr class="incident-table-row" data-incident-id="${incident.id}">
                                <td>${incident.incident_number || 'N/A'}</td>
                                <td>${incident.incident_type || 'Unknown'}</td>
                                <td><span class="priority-badge ${(incident.priority || 'medium').toLowerCase()}">${incident.priority || 'Medium'}</span></td>
                                <td><span class="status-badge ${(incident.status || 'open').toLowerCase()}">${incident.status || 'Open'}</span></td>
                                <td>${incident.location || 'N/A'}</td>
                                <td>${this.uiUtilities.formatDate(incident.created_at)}</td>
                                <td>
                                    <button class="action-btn view-btn" data-action="view" data-incident-id="${incident.id}">View</button>
                                    <button class="action-btn edit-btn" data-action="edit" data-incident-id="${incident.id}">Edit</button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            `;

            incidentsTable.innerHTML = tableHTML;

            // Add click handlers for actions
            incidentsTable.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = btn.dataset.action;
                    const incidentId = btn.dataset.incidentId;

                    if (action === 'view') {
                        this.selectIncident(incidentId);
                        // Switch to dispatch view to show details
                        document.querySelector('[data-view="dispatch"]').click();
                    } else if (action === 'edit') {
                        this.editIncident(incidentId);
                    }
                });
            });

        } catch (error) {
            console.error('Error loading list view:', error);
            incidentsTable.innerHTML = '<div class="error">Failed to load incidents</div>';
        }
    }

    async performIncidentSearch() {
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');

        if (!searchInput || !searchResults) return;

        const query = searchInput.value.trim();
        if (!query) {
            searchResults.innerHTML = `
                <div class="search-placeholder">
                    <div class="search-icon">🔍</div>
                    <p>Enter search terms to find incidents</p>
                </div>
            `;
            return;
        }

        try {
            searchResults.innerHTML = '<div class="loading">Searching...</div>';

            // Get all incidents
            const incidents = await this.data.search('incidents', {});

            // Filter incidents based on search query
            const filteredIncidents = incidents.filter(incident => {
                const searchFields = [
                    incident.incident_number,
                    incident.location,
                    incident.description,
                    incident.narrative,
                    incident.incident_type,
                    incident.reported_by
                ].filter(Boolean).join(' ').toLowerCase();

                return searchFields.includes(query.toLowerCase());
            });

            if (filteredIncidents.length === 0) {
                searchResults.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">❌</div>
                        <h3>No incidents found</h3>
                        <p>No incidents match your search criteria: "${query}"</p>
                    </div>
                `;
                return;
            }

            // Generate search results HTML
            const resultsHTML = `
                <div class="search-results-header">
                    <h4>Found ${filteredIncidents.length} incident(s)</h4>
                </div>
                <div class="search-results-list">
                    ${filteredIncidents.map(incident => `
                        <div class="search-result-item" data-incident-id="${incident.id}">
                            <div class="result-header">
                                <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                                <span class="incident-priority priority-${(incident.priority || 'medium').toLowerCase()}">${incident.priority || 'Medium'}</span>
                            </div>
                            <div class="result-type">${incident.incident_type || 'Unknown'}</div>
                            <div class="result-location">📍 ${incident.location}</div>
                            <div class="result-date">📅 ${this.uiUtilities.formatDate(incident.created_at)}</div>
                            <div class="result-description">${(incident.description || incident.narrative || '').substring(0, 100)}${(incident.description || incident.narrative || '').length > 100 ? '...' : ''}</div>
                        </div>
                    `).join('')}
                </div>
            `;

            searchResults.innerHTML = resultsHTML;

            // Add click handlers
            searchResults.querySelectorAll('.search-result-item').forEach(item => {
                item.addEventListener('click', () => {
                    const incidentId = item.dataset.incidentId;
                    // Switch to dispatch view and select the incident
                    document.querySelector('[data-view="dispatch"]').click();
                    setTimeout(() => {
                        this.selectIncident(incidentId);
                    }, 100);
                });
            });

        } catch (error) {
            console.error('Error performing search:', error);
            searchResults.innerHTML = '<div class="error">Search failed</div>';
        }
    }

    startIncidentsUpdates() {
        // Clear existing interval
        if (this.incidentsUpdateInterval) {
            clearInterval(this.incidentsUpdateInterval);
        }

        // Start polling for updates every 30 seconds
        this.incidentsUpdateInterval = setInterval(async () => {
            if (this.currentTab === 'incidents') {
                await this.loadIncidentsData();
            }
        }, 30000);
    }

    // All outreach management methods moved to outreach management module


    // applyIncidentFilters and filterIncidents methods removed - now handled by incident list manager


    // NOTE: geocodeIncidentAddress method moved to incident management address manager

    // NOTE: Original populateIncidentTabContent method has been replaced with delegation above

    // Initialize narrative editor for incident
    initializeNarrativeEditor(incident) {
        if (this.narrativeManagement) {
            // Initialize the narrative management system for the incident
            this.narrativeManagement.loadFromIncident(incident);
            this.narrativeManagement.init(incident.id, this.narrativeManagement.getEntries());
        } else {
            console.error('Narrative management not initialized');
        }
    }

    // Add narrative entry function for global access
    addNarrativeEntry(incidentId) {
        if (this.narrativeManagement) {
            // Set the current incident ID if provided
            if (incidentId) {
                this.narrativeManagement.currentIncidentId = incidentId;
            }
            this.narrativeManagement.addEntry();
        } else {
            console.error('Narrative management not initialized');
        }
    }

    // NOTE: Incident tab generation methods moved to incident management module templates

    // NOTE: formatTextForTerminal method moved to UI utilities module

    // closeIncident method moved to incident management module

    // deleteIncident method moved to incident management module

    async loadIncidentsData() {
        // Delegate to incident list manager if available
        if (this.incidentManagement && this.incidentManagement.listManager) {
            await this.incidentManagement.listManager.loadIncidentsData();
        } else {
            console.warn('Incident management not available, cannot load incidents data');
        }
    }

    // Show update status modal
    async showUpdateStatusModal(incidentId) {
        return await this.modalManagement.showUpdateStatusModal(incidentId);
    }

    closeUpdateStatusModal() {
        return this.modalManagement.closeUpdateStatusModal();
    }

    // updateIncidentStatus method moved to incident management module

    // syncStatusUpdateToSupabase method moved to incident management module

    async loadIncidentSearchContent() {
        return incidentListTemplates.incidentSearchInterface();
    }

    setupIncidentSearch() {
        const searchBtn = document.getElementById('perform-incident-search');
        const clearBtn = document.getElementById('clear-incident-search');
        const searchText = document.getElementById('incident-search-text');

        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.performIncidentSearch();
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                this.clearIncidentSearch();
            });
        }

        if (searchText) {
            searchText.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performIncidentSearch();
                }
            });
        }
    }

    async performIncidentSearch() {
        const searchText = document.getElementById('incident-search-text')?.value || '';
        const searchType = document.getElementById('incident-search-type')?.value || '';
        const searchStatus = document.getElementById('incident-search-status')?.value || '';
        const searchPriority = document.getElementById('incident-search-priority')?.value || '';
        const dateFrom = document.getElementById('incident-search-date-from')?.value || '';
        const dateTo = document.getElementById('incident-search-date-to')?.value || '';

        const resultsContainer = document.getElementById('incident-search-results');
        const resultsCount = document.getElementById('incident-results-count');

        if (!resultsContainer) return;

        try {
            // Show loading
            resultsContainer.innerHTML = '<div class="loading">Searching incidents...</div>';

            // Get all incidents
            const allIncidents = await this.data.search('incidents', {});

            // Apply filters
            let filteredIncidents = allIncidents.filter(incident => {
                // Text search
                if (searchText) {
                    const searchFields = [
                        incident.incident_number,
                        incident.location,
                        incident.description,
                        incident.narrative,
                        incident.incident_type,
                        incident.reported_by
                    ].filter(field => field);

                    const matchesText = searchFields.some(field =>
                        field.toLowerCase().includes(searchText.toLowerCase())
                    );
                    if (!matchesText) return false;
                }

                // Type filter
                if (searchType && incident.incident_type !== searchType) return false;

                // Status filter
                if (searchStatus && incident.status !== searchStatus) return false;

                // Priority filter
                if (searchPriority && incident.priority !== searchPriority) return false;

                // Date filters
                if (dateFrom || dateTo) {
                    const incidentDate = new Date(incident.created_at);
                    if (dateFrom && incidentDate < new Date(dateFrom)) return false;
                    if (dateTo && incidentDate > new Date(dateTo + 'T23:59:59')) return false;
                }

                return true;
            });

            // Sort by date (newest first)
            filteredIncidents.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

            // Update results count
            if (resultsCount) {
                resultsCount.textContent = `${filteredIncidents.length} incident${filteredIncidents.length !== 1 ? 's' : ''} found`;
            }

            if (filteredIncidents.length === 0) {
                resultsContainer.innerHTML = '<div class="no-results"><p>No incidents found matching your search criteria.</p></div>';
                return;
            }

            // Generate results HTML
            const resultsHTML = filteredIncidents.map(incident => `
                <div class="search-result-incident" data-incident-id="${incident.id}">
                    <div class="incident-summary">
                        <div class="incident-header">
                            <span class="incident-number">${incident.incident_number || 'N/A'}</span>
                            <span class="incident-date">${this.uiUtilities.formatDate(incident.created_at)}</span>
                            <span class="incident-priority ${(incident.priority || 'medium').toLowerCase()}">${(incident.priority || 'Medium').toUpperCase()}</span>
                        </div>
                        <div class="incident-type">${incident.incident_type || 'Unknown'}</div>
                        <div class="incident-location">📍 ${incident.location || 'No location'}</div>
                        <div class="incident-description">${(incident.description || incident.narrative || 'No description').substring(0, 150)}${(incident.description || incident.narrative || '').length > 150 ? '...' : ''}</div>
                        <div class="incident-status">Status: ${incident.status || 'Open'}</div>
                    </div>
                    <div class="incident-actions">
                        <button class="action-button" onclick="app.viewIncidentFromSearch('${incident.id}')">
                            View Details
                        </button>
                    </div>
                </div>
            `).join('');

            resultsContainer.innerHTML = resultsHTML;

        } catch (error) {
            console.error('Error searching incidents:', error);
            resultsContainer.innerHTML = '<div class="error">Error searching incidents. Please try again.</div>';
        }
    }

    clearIncidentSearch() {
        document.getElementById('incident-search-text').value = '';
        document.getElementById('incident-search-type').value = '';
        document.getElementById('incident-search-status').value = '';
        document.getElementById('incident-search-priority').value = '';
        document.getElementById('incident-search-date-from').value = '';
        document.getElementById('incident-search-date-to').value = '';

        const resultsContainer = document.getElementById('incident-search-results');
        const resultsCount = document.getElementById('incident-results-count');

        if (resultsContainer) {
            resultsContainer.innerHTML = '<div class="no-results"><p>Enter search criteria and click "Search Incidents" to find historical incidents.</p></div>';
        }

        if (resultsCount) {
            resultsCount.textContent = '0 incidents found';
        }
    }

    async viewIncidentFromSearch(incidentId) {
        // Navigate to incidents tab and select the incident
        await this.loadTabContent('incidents');
        setTimeout(() => {
            this.selectIncident(incidentId);
        }, 500);
    }

    // Temporary storage for incident creation links
    incidentCreationLinks = {
        people: [],
        vehicles: [],
        addresses: []
    };

    async showPersonLinkDialog() {
        try {
            const people = await this.data.search('people', {});

            if (!people || people.length === 0) {
                // Offer to create a new person
                const createNew = await this.ui.showConfirmDialog(
                    'No People Found',
                    'No people records found. Would you like to create a new person record?',
                    'Create New Person',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreatePersonDialog();
                }
                return;
            }

            const personOptions = people.map(person => ({
                value: person.id,
                label: `${person.first_name} ${person.last_name} - DOB: ${person.date_of_birth || 'Unknown'}`
            }));

            const fields = [
                {
                    name: 'person_id',
                    type: 'select',
                    label: 'Select Person',
                    options: [
                        { value: 'new', label: '+ Create New Person' },
                        ...personOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Party' },
                        { value: 'witness', label: 'Witness' },
                        { value: 'complainant', label: 'Complainant' },
                        { value: 'suspect', label: 'Suspect' },
                        { value: 'victim', label: 'Victim' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this person\'s involvement...'
                }
            ];

            this.ui.showForm('Link Person to Incident', fields, async (formData) => {
                if (formData.person_id === 'new') {
                    await this.showCreatePersonDialog();
                    return;
                }

                const person = people.find(p => p.id === formData.person_id);
                if (person) {
                    this.incidentCreationLinks.people.push({
                        record: person,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing person link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load people records', 'error');
        }
    }

    async showCreatePersonDialog() {
        const fields = [
            {
                name: 'first_name',
                type: 'text',
                label: 'First Name',
                required: true
            },
            {
                name: 'last_name',
                type: 'text',
                label: 'Last Name',
                required: true
            },
            {
                name: 'date_of_birth',
                type: 'date',
                label: 'Date of Birth'
            },
            {
                name: 'phone',
                type: 'tel',
                label: 'Phone Number'
            },
            {
                name: 'email',
                type: 'email',
                label: 'Email Address'
            }
        ];

        this.ui.showForm('Create New Person', fields, async (formData) => {
            try {
                const personData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newPerson = await this.data.insert('people', personData);
                this.ui.showDialog('Success', 'Person created successfully!', 'success');

                // Now show the link dialog again with the new person
                setTimeout(() => this.showPersonLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating person:', error);
                this.ui.showDialog('Error', 'Failed to create person record', 'error');
            }
        });
    }

    updateIncidentCreationLinksDisplay() {
        // Update people display
        const peopleContainer = document.getElementById('linked-people');
        if (peopleContainer) {
            if (this.incidentCreationLinks.people.length === 0) {
                peopleContainer.innerHTML = '<div class="no-links">No people linked yet</div>';
            } else {
                peopleContainer.innerHTML = this.incidentCreationLinks.people.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.first_name} ${link.record.last_name}
                                ${link.record.date_of_birth ? ` - DOB: ${link.record.date_of_birth}` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('people', ${index})">×</button>
                    </div>
                `).join('');
            }
        }

        // Update vehicles display
        const vehiclesContainer = document.getElementById('linked-vehicles');
        if (vehiclesContainer) {
            if (this.incidentCreationLinks.vehicles.length === 0) {
                vehiclesContainer.innerHTML = '<div class="no-links">No vehicles linked yet</div>';
            } else {
                vehiclesContainer.innerHTML = this.incidentCreationLinks.vehicles.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.license_plate} - ${link.record.make} ${link.record.model}
                                ${link.record.year ? ` (${link.record.year})` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('vehicles', ${index})">×</button>
                    </div>
                `).join('');
            }
        }

        // Update addresses display
        const addressesContainer = document.getElementById('linked-addresses');
        if (addressesContainer) {
            if (this.incidentCreationLinks.addresses.length === 0) {
                addressesContainer.innerHTML = '<div class="no-links">No addresses linked yet</div>';
            } else {
                addressesContainer.innerHTML = this.incidentCreationLinks.addresses.map((link, index) => `
                    <div class="linked-record-item">
                        <div class="linked-record-info">
                            <div class="linked-record-type">${link.link_type.toUpperCase()}</div>
                            <div class="linked-record-details">
                                ${link.record.street_address}, ${link.record.city}
                                ${link.record.province ? `, ${link.record.province}` : ''}
                            </div>
                            ${link.notes ? `<div class="linked-record-notes">${link.notes}</div>` : ''}
                        </div>
                        <button class="remove-link" onclick="app.removeIncidentCreationLink('addresses', ${index})">×</button>
                    </div>
                `).join('');
            }
        }
    }

    removeIncidentCreationLink(type, index) {
        this.incidentCreationLinks[type].splice(index, 1);
        this.updateIncidentCreationLinksDisplay();
    }

    async showVehicleLinkDialog() {
        try {
            const vehicles = await this.data.search('license_plates', {});

            if (!vehicles || vehicles.length === 0) {
                const createNew = await this.ui.showConfirmDialog(
                    'No Vehicles Found',
                    'No vehicle records found. Would you like to create a new vehicle record?',
                    'Create New Vehicle',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreateVehicleDialog();
                }
                return;
            }

            const vehicleOptions = vehicles.map(vehicle => ({
                value: vehicle.id,
                label: `${vehicle.license_plate} - ${vehicle.make} ${vehicle.model} ${vehicle.year || ''}`
            }));

            const fields = [
                {
                    name: 'vehicle_id',
                    type: 'select',
                    label: 'Select Vehicle',
                    options: [
                        { value: 'new', label: '+ Create New Vehicle' },
                        ...vehicleOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'involved', label: 'Involved Vehicle' },
                        { value: 'suspect', label: 'Suspect Vehicle' },
                        { value: 'witness', label: 'Witness Vehicle' },
                        { value: 'stolen', label: 'Stolen Vehicle' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this vehicle\'s involvement...'
                }
            ];

            this.ui.showForm('Link Vehicle to Incident', fields, async (formData) => {
                if (formData.vehicle_id === 'new') {
                    await this.showCreateVehicleDialog();
                    return;
                }

                const vehicle = vehicles.find(v => v.id === formData.vehicle_id);
                if (vehicle) {
                    this.incidentCreationLinks.vehicles.push({
                        record: vehicle,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing vehicle link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load vehicle records', 'error');
        }
    }

    async showCreateVehicleDialog() {
        const fields = [
            {
                name: 'license_plate',
                type: 'text',
                label: 'License Plate',
                required: true
            },
            {
                name: 'make',
                type: 'text',
                label: 'Make',
                required: true
            },
            {
                name: 'model',
                type: 'text',
                label: 'Model',
                required: true
            },
            {
                name: 'year',
                type: 'number',
                label: 'Year'
            },
            {
                name: 'color',
                type: 'text',
                label: 'Color'
            }
        ];

        this.ui.showForm('Create New Vehicle', fields, async (formData) => {
            try {
                const vehicleData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newVehicle = await this.data.insert('license_plates', vehicleData);
                this.ui.showDialog('Success', 'Vehicle created successfully!', 'success');

                setTimeout(() => this.showVehicleLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating vehicle:', error);
                this.ui.showDialog('Error', 'Failed to create vehicle record', 'error');
            }
        });
    }

    async showAddressLinkDialog() {
        try {
            const addresses = await this.data.search('addresses', {});

            if (!addresses || addresses.length === 0) {
                const createNew = await this.ui.showConfirmDialog(
                    'No Addresses Found',
                    'No address records found. Would you like to create a new address record?',
                    'Create New Address',
                    'Cancel'
                );

                if (createNew) {
                    await this.showCreateAddressDialog();
                }
                return;
            }

            const addressOptions = addresses.map(address => ({
                value: address.id,
                label: `${address.street_address}, ${address.city}${address.province ? `, ${address.province}` : ''}`
            }));

            const fields = [
                {
                    name: 'address_id',
                    type: 'select',
                    label: 'Select Address',
                    options: [
                        { value: 'new', label: '+ Create New Address' },
                        ...addressOptions
                    ],
                    required: true
                },
                {
                    name: 'link_type',
                    type: 'select',
                    label: 'Relationship Type',
                    options: [
                        { value: 'incident_location', label: 'Incident Location' },
                        { value: 'related_location', label: 'Related Location' },
                        { value: 'suspect_address', label: 'Suspect Address' },
                        { value: 'witness_address', label: 'Witness Address' },
                        { value: 'other', label: 'Other' }
                    ],
                    required: true
                },
                {
                    name: 'notes',
                    type: 'textarea',
                    label: 'Notes (Optional)',
                    placeholder: 'Additional details about this address...'
                }
            ];

            this.ui.showForm('Link Address to Incident', fields, async (formData) => {
                if (formData.address_id === 'new') {
                    await this.showCreateAddressDialog();
                    return;
                }

                const address = addresses.find(a => a.id === formData.address_id);
                if (address) {
                    this.incidentCreationLinks.addresses.push({
                        record: address,
                        link_type: formData.link_type,
                        notes: formData.notes || null
                    });
                    this.updateIncidentCreationLinksDisplay();
                }
            });

        } catch (error) {
            console.error('Error showing address link dialog:', error);
            this.ui.showDialog('Error', 'Failed to load address records', 'error');
        }
    }

    async showCreateAddressDialog() {
        const fields = [
            {
                name: 'street_address',
                type: 'text',
                label: 'Street Address',
                required: true
            },
            {
                name: 'city',
                type: 'text',
                label: 'City',
                required: true
            },
            {
                name: 'province',
                type: 'text',
                label: 'Province',
                required: true
            },
            {
                name: 'postal_code',
                type: 'text',
                label: 'Postal Code'
            }
        ];

        this.ui.showForm('Create New Address', fields, async (formData) => {
            try {
                const addressData = {
                    ...formData,
                    created_at: new Date().toISOString(),
                    created_by: this.auth.getCurrentUser()?.email || 'System'
                };

                const newAddress = await this.data.insert('addresses', addressData);
                this.ui.showDialog('Success', 'Address created successfully!', 'success');

                setTimeout(() => this.showAddressLinkDialog(), 500);

            } catch (error) {
                console.error('Error creating address:', error);
                this.ui.showDialog('Error', 'Failed to create address record', 'error');
            }
        });
    }

    // editIncident method removed - now handled by incident management module's editIncidentWorkflow

    async showIncidentPrintPreview(incident) {
        try {
            console.log('Opening print preview for incident:', incident.id);

            // Load incident people relationships if they exist
            await this.incidentManagement.peopleManager.loadIncidentPeopleRelationships(incident);

            // Load narrative entries using the NarrativeManagement logic
            if (this.narrativeManagement) {
                this.narrativeManagement.loadFromIncident(incident);
                incident.narrativeEntries = this.narrativeManagement.getEntries();
            }

            // Create print preview window
            const printWindow = window.open('', '_blank', 'width=800,height=900,scrollbars=yes,resizable=yes');
            
            if (!printWindow) {
                this.ui.showDialog('Error', 'Pop-up blocked. Please allow pop-ups for this site and try again.', 'error');
                return;
            }

            // Generate the print HTML content
            const printContent = incidentDetailTemplates.printReport(incident);
            
            // Write the content to the new window
            printWindow.document.write(printContent);
            printWindow.document.close();

            // Add print functionality when the window loads
            printWindow.onload = function() {
                // Add a print button to the preview
                const printButton = printWindow.document.createElement('button');
                printButton.textContent = 'Print Report';
                printButton.style.cssText = `
                    position: fixed;
                    top: 10px;
                    right: 10px;
                    z-index: 1000;
                    padding: 10px 20px;
                    background: #007bff;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    cursor: pointer;
                    font-family: Arial, sans-serif;
                    font-size: 14px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    @media print { display: none; }
                `;
                
                printButton.onclick = function() {
                    printWindow.print();
                };
                
                printWindow.document.body.appendChild(printButton);

                // Focus the window for better user experience
                printWindow.focus();
            };

            this.uiUtilities.showToast('Print preview opened in new window', 'success');

        } catch (error) {
            console.error('Error opening print preview:', error);
            this.ui.showDialog('Error', 'Failed to open print preview', 'error');
        }
    }

    async removeLinkFromIncident(linkId, incidentId) {
        try {
            const confirmed = await this.ui.showConfirmDialog(
                'Remove Link',
                'Are you sure you want to remove this link from the incident?',
                'Remove',
                'Cancel'
            );

            if (!confirmed) return;

            await this.data.delete('incident_links', linkId);
            this.uiUtilities.showToast('Link removed successfully', 'success');

            // Refresh the links display
            this.loadIncidentLinks(incidentId);

        } catch (error) {
            console.error('Error removing link:', error);
            this.uiUtilities.showToast('Failed to remove link', 'error');
        }
    }

    async loadEditIncidentContent() {
        console.log('Loading edit incident content...');

        // Get the incident data
        const incident = await this.data.get('incidents', this.editingIncidentId);
        if (!incident) {
            return `
                <div class="error-container">
                    <h2>Incident Not Found</h2>
                    <p>The incident you're trying to edit could not be found.</p>
                    <button class="secondary-button" data-action="back-to-incidents">
                        ← Back to Incidents
                    </button>
                </div>
            `;
        }

        return incidentFormTemplates.editIncidentForm(incident);
    }

    async loadComprehensiveIncidentForm() {
        console.log('Loading comprehensive incident creation form...');
        return incidentFormTemplates.createIncidentForm();
    }

    setupComprehensiveIncidentForm() {
        console.log('Setting up comprehensive incident form...');
        
        // Initialize tab switching
        this.initializeIncidentTabs();
        
        // Initialize conditional field visibility
        this.initializeConditionalFields();
        
        // Initialize people management for the incident form
        this.initializePeopleManagement();
        
        // Handle form submission
        const form = document.getElementById('comprehensive-incident-form');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.incidentManagement.formManager.handleComprehensiveIncidentSubmit();
            });
        }

        // Handle Save Draft button
        const saveDraftButton = document.getElementById('save-draft');
        if (saveDraftButton) {
            saveDraftButton.addEventListener('click', (e) => {
                e.preventDefault();
                this.incidentManagement.saveDraftIncident();
            });
        }

        // Handle Back and Cancel buttons
        const backButton = document.querySelector('.back-to-incidents');
        const cancelButton = document.querySelector('.cancel-incident-form');
        
        if (backButton) {
            backButton.addEventListener('click', () => {
                this.loadTabContent('incidents');
            });
        }
        
        if (cancelButton) {
            cancelButton.addEventListener('click', () => {
                this.loadTabContent('incidents');
            });
        }

        // Initialize people management
        this.initializePeopleManagement();
        
        // Initialize Google Maps address search - now handled by incident-address-manager.js
    }

    initializeIncidentTabs() {
        const tabs = document.querySelectorAll('.incident-form-tab');
        const contents = document.querySelectorAll('.tab-content');

        // Tab switching
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetTab = tab.dataset.tab;

                // Update tab appearance
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');

                // Update content visibility
                contents.forEach(content => {
                    content.classList.remove('active');
                    if (content.dataset.tab === targetTab) {
                        content.classList.add('active');
                    }
                });
            });
        });
    }

    initializeConditionalFields() {
        // Use current time checkbox
        const useCurrentTimeCheckbox = document.querySelector('input[name="use_current_time"]');
        const datetimeGroup = document.querySelector('#datetime-group');
        const datetimeGroupTime = document.querySelector('#datetime-group-time');
        const incidentDateField = document.querySelector('#incident_date');
        const incidentTimeField = document.querySelector('#incident_time');

        // Function to populate current date and time
        const populateCurrentDateTime = () => {
            const now = new Date();
            if (incidentDateField) {
                incidentDateField.value = now.toISOString().split('T')[0];
            }
            if (incidentTimeField) {
                incidentTimeField.value = now.toTimeString().slice(0, 5);
            }
        };

        // Check if this is an edit form by looking for the incident-id hidden field
        const isEditForm = document.getElementById('incident-id') !== null;

        // Only auto-populate current date/time for new incidents (create form)
        // For edit forms, preserve the existing values that are already in the form fields
        if (!isEditForm) {
            // Initialize with current date/time when creating a new incident
            populateCurrentDateTime();
        }

        if (useCurrentTimeCheckbox) {
            useCurrentTimeCheckbox.addEventListener('change', () => {
                if (useCurrentTimeCheckbox.checked) {
                    if (datetimeGroup) datetimeGroup.style.display = 'none';
                    if (datetimeGroupTime) datetimeGroupTime.style.display = 'none';
                    // When checked, always populate with current date and time
                    populateCurrentDateTime();
                } else {
                    if (datetimeGroup) datetimeGroup.style.display = 'block';
                    if (datetimeGroupTime) datetimeGroupTime.style.display = 'block';
                    // When unchecked in create form, populate with current date and time
                    // When unchecked in edit form, preserve existing values (don't overwrite)
                    if (!isEditForm) {
                        populateCurrentDateTime();
                    }
                }
            });
        }

        // Police notification checkbox
        const policeNotifiedCheckbox = document.querySelector('input[name="police_notified"]');
        const policeDetails = document.querySelector('#police-response-details');
        
        if (policeNotifiedCheckbox && policeDetails) {
            policeNotifiedCheckbox.addEventListener('change', () => {
                policeDetails.style.display = policeNotifiedCheckbox.checked ? 'block' : 'none';
            });
        }

        // Fire department checkbox
        const fireCheckbox = document.querySelector('input[name="fire_department_called"]');
        const fireDetails = document.querySelector('#fire-response-details');
        
        if (fireCheckbox && fireDetails) {
            fireCheckbox.addEventListener('change', () => {
                fireDetails.style.display = fireCheckbox.checked ? 'block' : 'none';
            });
        }

        // Paramedics checkbox
        const paramedicCheckbox = document.querySelector('input[name="paramedics_called"]');
        const paramedicDetails = document.querySelector('#paramedic-response-details');
        
        if (paramedicCheckbox && paramedicDetails) {
            paramedicCheckbox.addEventListener('change', () => {
                paramedicDetails.style.display = paramedicCheckbox.checked ? 'block' : 'none';
            });
        }

        // Bylaw notification checkbox
        const bylawCheckbox = document.querySelector('input[name="bylaw_notified"]');
        const bylawDetails = document.querySelector('#bylaw-response-details');

        if (bylawCheckbox && bylawDetails) {
            bylawCheckbox.addEventListener('change', () => {
                bylawDetails.style.display = bylawCheckbox.checked ? 'block' : 'none';
            });
        }

        // Property recovered checkbox
        const propertyRecoveredCheckbox = document.querySelector('input[name="property_recovered"]');
        const propertyDetails = document.querySelector('#property-details');
        
        if (propertyRecoveredCheckbox && propertyDetails) {
            propertyRecoveredCheckbox.addEventListener('change', () => {
                propertyDetails.style.display = propertyRecoveredCheckbox.checked ? 'block' : 'none';
            });
        }

        // Follow-up required checkbox
        const followupCheckbox = document.querySelector('input[name="follow_up_required"]');
        const followupDetails = document.querySelector('#followup-details');
        
        if (followupCheckbox && followupDetails) {
            followupCheckbox.addEventListener('change', () => {
                followupDetails.style.display = followupCheckbox.checked ? 'block' : 'none';
            });
        }
    }

    // handleComprehensiveIncidentSubmit() method removed - now handled by incident-form-manager.js

    async validateIncidentData(incidentData) {
        const errors = [];

        // Validate scene_safety constraint
        if (incidentData.scene_safety && incidentData.scene_safety !== '') {
            const validSceneSafetyValues = ['safe', 'caution', 'unsafe', 'hazardous'];
            if (!validSceneSafetyValues.includes(incidentData.scene_safety)) {
                errors.push(`Scene safety must be one of: ${validSceneSafetyValues.join(', ')}`);
            }
        }

        // Validate priority constraint
        if (incidentData.priority && incidentData.priority !== '') {
            const validPriorityValues = ['low', 'medium', 'high'];
            if (!validPriorityValues.includes(incidentData.priority.toLowerCase())) {
                errors.push(`Priority must be one of: ${validPriorityValues.join(', ')}`);
            }
        }

        // Validate required fields
        if (!incidentData.narrative && !incidentData.description) {
            errors.push('Either narrative or description is required');
        }

        if (!incidentData.location) {
            errors.push('Location is required');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    async saveDraftIncident() {
        // Delegate to incident management module
        if (this.incidentManagement) {
            return await this.incidentManagement.saveDraftIncident();
        }

        // Fallback for legacy compatibility
        try {
            const form = document.getElementById('comprehensive-incident-form');
            if (!form) return;

            // Collect form data
            const formData = new FormData(form);
            const draftData = {};

            // Process form fields 
            for (const [key, value] of formData.entries()) {
                if (key.endsWith('[]') || ['services_offered', 'services_provided', 'resources_distributed', 'referrals_made', 'substance_indicators', 'environmental_factors'].includes(key)) {
                    // Handle array fields
                    const arrayKey = key.replace('[]', '');
                    if (!draftData[arrayKey]) {
                        draftData[arrayKey] = [];
                    }
                    draftData[arrayKey].push(value);
                } else {
                    draftData[key] = value;
                }
            }

            // Handle checkboxes that weren't checked
            // Note: 'use_current_time' is excluded as it's only for UI logic, not database storage
            const checkboxFields = ['police_notified', 'fire_department_called', 'paramedics_called', 'follow_up_required', 'bylaw_notified'];
            checkboxFields.forEach(field => {
                if (!formData.has(field)) {
                    draftData[field] = false;
                }
            });

            // Fix field mapping issues
            if (draftData.police_required !== undefined) {
                draftData.police_notified = draftData.police_required;
                delete draftData.police_required;
            }

            // Set default values for medical fields that may not be in the form
            draftData.hospital_transport_offered = formData.has('hospital_transport_offered') ? true : false;
            draftData.transport_declined = formData.has('transport_declined') ? true : false;

            // Remove property-related fields that belong in property_records table
            const propertyFields = ['property_type', 'property_description', 'recovery_method', 'disposition_type',
                                  'property_brand', 'property_model', 'property_serial', 'property_value',
                                  'property_condition', 'property_recovered'];
            propertyFields.forEach(field => {
                delete draftData[field];
            });

            // Remove use_current_time from draft data (it's UI-only)
            delete draftData.use_current_time;

            // Map form fields to database requirements
            // Location field - combine address components or use address_search
            if (!draftData.location) {
                draftData.location = draftData.address_search ||
                    [draftData.street_address, draftData.city, draftData.province]
                        .filter(Boolean).join(', ') ||
                    'Location not specified';
            }

            // Narrative field - map from description
            if (!draftData.narrative && draftData.description) {
                draftData.narrative = draftData.description;
            }

            // Ensure required fields have values for drafts (can be minimal)
            if (!draftData.narrative) {
                draftData.narrative = draftData.description || 'Draft - no description yet';
            }
            if (!draftData.location) {
                draftData.location = 'Draft - location not specified';
            }

            // Clean up any undefined values
            Object.keys(draftData).forEach(key => {
                if (draftData[key] === undefined) {
                    draftData[key] = null;
                }
                // Handle scene_safety constraint - convert empty string to null to avoid constraint violation
                if (key === 'scene_safety' && draftData[key] === '') {
                    draftData[key] = null;
                }
            });

            // Add draft metadata
            draftData.is_draft = true;
            draftData.draft_created_at = new Date().toISOString();
            draftData.created_by = this.auth.getCurrentUser()?.email;
            draftData.status = 'draft';

            console.log('📝 Saving draft with data:', draftData);
            console.log('📝 Draft data keys:', Object.keys(draftData));

            // Force schema update before inserting
            if (this.data.sqlite) {
                this.data.sqlite.forceIncidentSchemaUpdate();
            }

            // Save to Supabase as draft incident
            try {
                const result = await this.data.insert('incidents', draftData);
                if (result && result.id) {
                    console.log(`Draft incident saved to Supabase with ID: ${result.id}`);
                    
                    // Save people relationships if any were added
                    if (this.involvedPeople && this.involvedPeople.length > 0) {
                        console.log(`Saving ${this.involvedPeople.length} people relationships for draft incident ${result.id}`);
                        await this.incidentManagement.peopleManager.saveIncidentPeopleRelationships(result.id, this.involvedPeople);
                    }
                    
                    this.uiUtilities.showToast('✅ Draft saved to server successfully', 'success');
                    
                    // Also save to localStorage as backup
                    const drafts = JSON.parse(localStorage.getItem('incident_drafts') || '[]');
                    draftData.id = result.id; // Use the Supabase ID
                    drafts.push(draftData);
                    localStorage.setItem('incident_drafts', JSON.stringify(drafts));
                } else {
                    throw new Error('Failed to save draft to server');
                }
            } catch (supabaseError) {
                console.warn('Failed to save draft to Supabase, falling back to localStorage:', supabaseError);
                // Fallback to localStorage only
                const drafts = JSON.parse(localStorage.getItem('incident_drafts') || '[]');
                const draftId = Date.now().toString();
                draftData.draft_id = draftId;
                
                // Include people relationships in localStorage fallback
                if (this.involvedPeople && this.involvedPeople.length > 0) {
                    draftData.people_involved = JSON.stringify(this.involvedPeople);
                    console.log(`Including ${this.involvedPeople.length} people relationships in localStorage draft`);
                }
                
                drafts.push(draftData);
                localStorage.setItem('incident_drafts', JSON.stringify(drafts));
                this.uiUtilities.showToast('✅ Draft saved locally (offline)', 'success');
            }
            
        } catch (error) {
            console.error('❌ Error saving draft:', error);
            this.uiUtilities.showToast('❌ Failed to save draft: ' + error.message, 'error');
        }
    }

    // Draft Management Methods - now simplified
    async loadDraftIncidents() {
        // Drafts are now just regular incidents with status 'draft'
        // They appear in the main incident list, no special loading needed
        try {
            const currentUser = this.auth.getCurrentUser();
            if (!currentUser) {
                console.warn('No authenticated user for draft loading');
                return [];
            }

            // Get draft incidents from the regular incidents table
            const drafts = await this.data.search('incidents', {
                status: 'draft',
                created_by: currentUser.email
            });
            console.log(`Found ${drafts.length} draft incidents`);
            return drafts;
        } catch (error) {
            console.error('Failed to load draft incidents:', error);
            return [];
        }
    }

    async loadDraftIntoForm(draftId) {
        try {
            console.log(`Loading draft incident ${draftId} into form...`);
            
            // Try to load from Supabase first
            let draft = null;
            try {
                draft = await this.data.get('incidents', draftId);
            } catch (error) {
                console.warn('Failed to load draft from Supabase, trying localStorage:', error);
            }

            // Fallback to localStorage
            if (!draft) {
                const localDrafts = JSON.parse(localStorage.getItem('incident_drafts') || '[]');
                draft = localDrafts.find(d => d.id == draftId);
            }

            if (!draft) {
                throw new Error('Draft not found');
            }

            // Load the incident creation form
            await this.loadCreateIncidentContent();

            // Wait for form to be initialized
            await new Promise(resolve => setTimeout(resolve, 100));

            // Populate form with draft data
            this.populateFormWithDraftData(draft);

            this.uiUtilities.showToast(`✅ Draft incident loaded`, 'success');
        } catch (error) {
            console.error('Error loading draft into form:', error);
            this.uiUtilities.showToast('❌ Failed to load draft: ' + error.message, 'error');
        }
    }

    populateFormWithDraftData(draft) {
        try {
            // Populate basic fields
            const form = document.getElementById('comprehensive-incident-form');
            if (!form) {
                throw new Error('Incident form not found');
            }

            Object.keys(draft).forEach(key => {
                const element = form.querySelector(`[name="${key}"]`);
                if (element && draft[key] !== null && draft[key] !== undefined) {
                    if (element.type === 'checkbox') {
                        element.checked = !!draft[key];
                    } else if (element.type === 'radio') {
                        if (element.value === draft[key]) {
                            element.checked = true;
                        }
                    } else {
                        element.value = draft[key];
                    }
                }
            });

            // Handle special cases (checkboxes, arrays, etc.)
            this.populateArrayFields(draft);
            this.populateInvolvedPeople(draft);

            console.log('Form populated with draft data');
        } catch (error) {
            console.error('Error populating form with draft data:', error);
        }
    }

    populateArrayFields(draft) {
        // Handle services, resources, referrals, etc.
        const arrayFields = [
            'services_offered', 'services_provided', 
            'resources_distributed', 'referrals_made',
            'substance_indicators', 'environmental_factors'
        ];

        arrayFields.forEach(fieldName => {
            if (draft[fieldName]) {
                let values = Array.isArray(draft[fieldName]) ? draft[fieldName] : 
                            typeof draft[fieldName] === 'string' ? draft[fieldName].split(',') : [];
                
                values.forEach(value => {
                    const checkbox = document.querySelector(`input[name="${fieldName}"][value="${value.trim()}"]`);
                    if (checkbox) {
                        checkbox.checked = true;
                    }
                });
            }
        });
    }

    populateInvolvedPeople(draft) {
        // Restore involved people list
        if (draft.incident_people || draft.people_involved) {
            try {
                const peopleData = draft.incident_people || draft.people_involved;
                const people = typeof peopleData === 'string' ? JSON.parse(peopleData) : peopleData;
                
                if (Array.isArray(people)) {
                    this.involvedPeople = people;
                    this.updateInvolvedPeopleDisplay();
                }
            } catch (error) {
                console.warn('Failed to parse involved people data:', error);
            }
        }
    }

    async deleteDraft(draftId) {
        try {
            console.log(`Deleting draft incident ${draftId}...`);
            
            // Try to delete from Supabase
            try {
                await this.data.delete('incidents', draftId);
                console.log('Draft deleted from Supabase');
            } catch (error) {
                console.warn('Failed to delete from Supabase, removing from localStorage:', error);
            }

            // Remove from localStorage as well
            const localDrafts = JSON.parse(localStorage.getItem('incident_drafts') || '[]');
            const filteredDrafts = localDrafts.filter(d => d.id != draftId);
            localStorage.setItem('incident_drafts', JSON.stringify(filteredDrafts));

            // Refresh incidents list
            this.loadIncidentsData();

            this.uiUtilities.showToast('✅ Draft deleted', 'success');
        } catch (error) {
            console.error('Error deleting draft:', error);
            this.uiUtilities.showToast('❌ Failed to delete draft: ' + error.message, 'error');
        }
    }

    // People Management for Incident Forms
    initializePeopleManagement() {
        console.log('Initializing people management...');
        
        // Store involved people (in-memory for form session)
        this.involvedPeople = [];
        
        // Set up event handlers
        const searchBtn = document.getElementById('search-people-btn');
        const searchInput = document.getElementById('people-search');
        const addUnknownBtn = document.getElementById('add-unknown-person-btn');
        
        if (searchBtn && searchInput) {
            searchBtn.addEventListener('click', () => this.searchPeopleForIncident());
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.searchPeopleForIncident();
                }
            });
        }
        
        if (addUnknownBtn) {
            addUnknownBtn.addEventListener('click', () => this.showAddUnknownPersonDialog());
        }
        
        // Update the display
        this.updateInvolvedPeopleDisplay();
    }

    async searchPeopleForIncident() {
        const searchInput = document.getElementById('people-search');
        const resultsContainer = document.getElementById('people-search-results');
        const resultsList = document.getElementById('search-results-list');
        
        if (!searchInput || !resultsContainer || !resultsList) return;
        
        const query = searchInput.value.trim();
        if (!query) {
            resultsContainer.style.display = 'none';
            return;
        }
        
        try {
            console.log('Searching for people:', query);
            
            // Search people by name
            const searchResults = await this.data.search('people', {
                searchFields: ['first_name', 'last_name'],
                searchQuery: query,
                limit: 10
            });
            
            if (searchResults.length === 0) {
                resultsList.innerHTML = `
                    <div class="no-results">
                        <p>No people found matching "${query}"</p>
                        <button type="button" class="action-btn" onclick="window.app.showCreatePersonDialog('${query}')">
                            CREATE NEW PERSON
                        </button>
                    </div>
                `;
            } else {
                resultsList.innerHTML = searchResults.map(person => `
                    <div class="search-result-item" data-person-id="${person.id}">
                        <div class="person-info">
                            <strong>${person.first_name} ${person.last_name}</strong>
                            ${person.age ? `<span class="person-age">(Age: ${person.age})</span>` : ''}
                            ${person.housing_status ? `<span class="person-status">${person.housing_status}</span>` : ''}
                        </div>
                        <button type="button" class="action-btn small" onclick="window.app.addPersonToIncident(${person.id})">
                            ADD TO INCIDENT
                        </button>
                    </div>
                `).join('');
            }
            
            resultsContainer.style.display = 'block';
            
        } catch (error) {
            console.error('Error searching people:', error);
            this.uiUtilities.showToast('❌ Error searching people', 'error');
        }
    }

    async addPersonToIncident(personId) {
        try {
            // Check if person is already added
            if (this.involvedPeople.find(p => p.person_id === personId)) {
                this.uiUtilities.showToast('⚠️ Person already added to incident', 'warning');
                return;
            }
            
            // Get person details
            const person = await this.data.get('people', personId);
            if (!person) {
                this.uiUtilities.showToast('❌ Person not found', 'error');
                return;
            }
            
            // Show involvement type dialog
            this.showInvolvementTypeDialog(person);
            
        } catch (error) {
            console.error('Error adding person to incident:', error);
            this.uiUtilities.showToast('❌ Error adding person', 'error');
        }
    }

    showInvolvementTypeDialog(person) {
        const involvementTypes = [
            { value: 'witness', label: 'Witness' },
            { value: 'victim', label: 'Victim' },
            { value: 'suspect', label: 'Suspect' },
            { value: 'complainant', label: 'Complainant' },
            { value: 'subject', label: 'Subject' },
            { value: 'family_member', label: 'Family Member' },
            { value: 'service_recipient', label: 'Service Recipient' },
            { value: 'other', label: 'Other' }
        ];
        
        const dialogHtml = `
            <div class="dialog-overlay" id="involvement-type-dialog">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>Add Person to Incident</h3>
                    </div>
                    <div class="dialog-body">
                        <p><strong>${person.first_name} ${person.last_name}</strong></p>
                        <div class="form-field">
                            <label for="involvement-type">Involvement Type:</label>
                            <select id="involvement-type" required>
                                <option value="">Select type...</option>
                                ${involvementTypes.map(type => 
                                    `<option value="${type.value}">${type.label}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="involvement-notes">Notes (optional):</label>
                            <textarea id="involvement-notes" placeholder="Additional notes about this person's involvement" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button type="button" class="action-btn" onclick="window.app.confirmAddPersonToIncident(${person.id})">ADD PERSON</button>
                        <button type="button" class="action-btn" onclick="window.app.closeInvolvementDialog()">CANCEL</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', dialogHtml);
    }

    confirmAddPersonToIncident(personId) {
        const typeSelect = document.getElementById('involvement-type');
        const notesTextarea = document.getElementById('involvement-notes');
        
        if (!typeSelect || !typeSelect.value) {
            this.uiUtilities.showToast('⚠️ Please select an involvement type', 'warning');
            return;
        }
        
        const person = this.involvedPeople.find(p => p.person_id === personId);
        if (person) {
            this.uiUtilities.showToast('⚠️ Person already added', 'warning');
            this.closeInvolvementDialog();
            return;
        }
        
        // Add person to involved list
        this.involvedPeople.push({
            person_id: personId,
            involvement_type: typeSelect.value,
            is_unknown_party: false,
            notes: notesTextarea ? notesTextarea.value : '',
            person_data: null // Will be populated when displaying
        });
        
                
        this.updateInvolvedPeopleDisplay();
        this.closeInvolvementDialog();
        this.uiUtilities.showToast('✅ Person added to incident', 'success');
        
        // Hide search results
        const resultsContainer = document.getElementById('people-search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
        
        // Clear search input
        const searchInput = document.getElementById('people-search');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    showAddUnknownPersonDialog() {
        const involvementTypes = [
            { value: 'witness', label: 'Witness' },
            { value: 'victim', label: 'Victim' },
            { value: 'suspect', label: 'Suspect' },
            { value: 'complainant', label: 'Complainant' },
            { value: 'subject', label: 'Subject' },
            { value: 'family_member', label: 'Family Member' },
            { value: 'service_recipient', label: 'Service Recipient' },
            { value: 'other', label: 'Other' }
        ];
        
        const dialogHtml = `
            <div class="dialog-overlay" id="unknown-person-dialog">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>Add Unknown Person</h3>
                    </div>
                    <div class="dialog-body">
                        <div class="form-field">
                            <label for="unknown-description">Description:</label>
                            <textarea id="unknown-description" placeholder="e.g., 'White male, approximately 30 years old, wearing blue jacket'" rows="3" required></textarea>
                        </div>
                        <div class="form-field">
                            <label for="unknown-involvement-type">Involvement Type:</label>
                            <select id="unknown-involvement-type" required>
                                <option value="">Select type...</option>
                                ${involvementTypes.map(type => 
                                    `<option value="${type.value}">${type.label}</option>`
                                ).join('')}
                            </select>
                        </div>
                        <div class="form-field">
                            <label for="unknown-notes">Additional Notes:</label>
                            <textarea id="unknown-notes" placeholder="Any additional information about this person" rows="2"></textarea>
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button type="button" class="action-btn" onclick="window.app.confirmAddUnknownPerson()">ADD UNKNOWN PERSON</button>
                        <button type="button" class="action-btn" onclick="window.app.closeUnknownPersonDialog()">CANCEL</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', dialogHtml);
    }

    confirmAddUnknownPerson() {
        const descriptionTextarea = document.getElementById('unknown-description');
        const typeSelect = document.getElementById('unknown-involvement-type');
        const notesTextarea = document.getElementById('unknown-notes');
        
        if (!descriptionTextarea || !descriptionTextarea.value.trim()) {
            this.uiUtilities.showToast('⚠️ Please provide a description', 'warning');
            return;
        }
        
        if (!typeSelect || !typeSelect.value) {
            this.uiUtilities.showToast('⚠️ Please select an involvement type', 'warning');
            return;
        }
        
        // Add unknown person to involved list
        this.involvedPeople.push({
            person_id: null,
            involvement_type: typeSelect.value,
            is_unknown_party: true,
            unknown_party_description: descriptionTextarea.value.trim(),
            notes: notesTextarea ? notesTextarea.value.trim() : ''
        });
        
                
        this.updateInvolvedPeopleDisplay();
        this.closeUnknownPersonDialog();
        this.uiUtilities.showToast('✅ Unknown person added to incident', 'success');
    }

    async updateInvolvedPeopleDisplay() {
        const container = document.getElementById('people-list-container');
        if (!container) return;
        
        if (this.involvedPeople.length === 0) {
            container.innerHTML = '<div class="no-people-message">No people have been added to this incident yet.</div>';
            return;
        }
        
        // Load person data for known people
        for (let involvement of this.involvedPeople) {
            if (involvement.person_id && !involvement.person_data) {
                try {
                    involvement.person_data = await this.data.get('people', involvement.person_id);
                } catch (error) {
                    console.error('Error loading person data:', error);
                }
            }
        }
        
        container.innerHTML = this.involvedPeople.map((involvement, index) => {
            const isUnknown = involvement.is_unknown_party;
            const person = involvement.person_data;
            const medical = involvement.medical_info || {};
            
            return `
                <div class="involved-person-item" id="person-item-${index}">
                    <div class="person-details">
                        <div class="person-header">
                            <div class="person-name">
                                ${isUnknown 
                                    ? `<strong>Unknown Person</strong>` 
                                    : `<strong>${person?.first_name || 'Unknown'} ${person?.last_name || 'Person'}</strong>`
                                }
                            </div>
                            <div class="person-role">
                                <span class="involvement-badge involvement-${involvement.involvement_type}">
                                    ${involvement.involvement_type.replace('_', ' ').toUpperCase()}
                                </span>
                                ${medical.required_medical_attention ? `<span class="medical-badge">MEDICAL</span>` : ''}
                            </div>
                        </div>
                        
                        ${isUnknown 
                            ? `<div class="person-description">${involvement.unknown_party_description}</div>`
                            : person ? `<div class="person-info">
                                ${person.age ? `Age: ${person.age} | ` : ''}
                                ${person.housing_status || 'Unknown housing status'}
                               </div>` : ''
                        }
                        
                        ${involvement.notes ? `<div class="person-notes"><strong>Notes:</strong> ${involvement.notes}</div>` : ''}
                        
                        ${medical.required_medical_attention ? `
                            <div class="person-medical-summary">
                                <div class="medical-summary-header">
                                    <strong>Medical Information:</strong>
                                </div>
                                ${medical.medical_issue_type ? `<div class="medical-issue">Issue: ${this.formatMedicalIssueType(medical.medical_issue_type)}</div>` : ''}
                                ${medical.first_aid_provided ? `<div class="first-aid-info">First aid provided by IHARC</div>` : ''}
                                ${medical.hospital_transport_offered && medical.transport_declined ? `<div class="transport-info">Transport declined</div>` : 
                                  medical.hospital_transport_offered ? `<div class="transport-info">Transported to hospital</div>` : ''}
                            </div>
                        ` : ''}
                    </div>
                    
                    <div class="person-actions">
                        <button type="button" class="action-btn small" onclick="window.app.showMedicalInfoDialog(${index})">
                            ${medical.required_medical_attention ? 'EDIT MEDICAL' : 'ADD MEDICAL'}
                        </button>
                        <button type="button" class="action-btn danger small" onclick="window.app.removePersonFromIncident(${index})"
                            REMOVE
                        </button>
                    </div>
                </div>
            `;
        }).join('');
    }

    async removePersonFromIncident(index) {
        if (index >= 0 && index < this.involvedPeople.length) {
            const personToRemove = this.involvedPeople[index];
            
            // If we're editing an existing incident and this person has a valid person_id, 
            // delete their associated activity
            if (this.editingIncidentId && personToRemove.person_id && !personToRemove.is_unknown_party) {
                await this.deleteIncidentActivity(this.editingIncidentId, personToRemove.person_id);
            }
            
            this.involvedPeople.splice(index, 1);
            this.updateInvolvedPeopleDisplay();
            this.uiUtilities.showToast('✅ Person removed from incident', 'success');
        }
    }

    closeInvolvementDialog() {
        const dialog = document.getElementById('involvement-type-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    closeUnknownPersonDialog() {
        const dialog = document.getElementById('unknown-person-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    // Modal functions for incident edit form
    showPersonSelectionModal() {
        // This function is called from the incident edit form
        // It should trigger the people search functionality
        console.log('showPersonSelectionModal called');

        // Focus on the people search input if it exists
        const searchInput = document.getElementById('people-search');
        if (searchInput) {
            searchInput.focus();
            // Scroll to the people search section
            searchInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
            // If we're not in the incident form context, show a dialog
            this.showPersonLinkDialog();
        }
    }

    showUnknownPersonModal() {
        // This function is called from the incident edit form
        // It should trigger the unknown person dialog
        console.log('showUnknownPersonModal called');

        // Check if we're in the incident form context
        const addUnknownBtn = document.getElementById('add-unknown-person-btn');
        if (addUnknownBtn) {
            // We're in the incident form, use the existing functionality
            this.showAddUnknownPersonDialog();
        } else {
            // Fallback to a generic dialog
            this.ui.showDialog('Add Unknown Person', 'This feature is not available in this context.', 'info');
        }
    }

    async showCreatePersonDialog(searchQuery = '') {
        const names = searchQuery.split(' ');
        const firstName = names[0] || '';
        const lastName = names.slice(1).join(' ') || '';
        
        const dialogHtml = `
            <div class="dialog-overlay" id="create-person-dialog">
                <div class="dialog-content">
                    <div class="dialog-header">
                        <h3>Create New Person</h3>
                    </div>
                    <div class="dialog-body">
                        <div class="form-field">
                            <label for="new-first-name">First Name:</label>
                            <input type="text" id="new-first-name" value="${firstName}" required>
                        </div>
                        <div class="form-field">
                            <label for="new-last-name">Last Name:</label>
                            <input type="text" id="new-last-name" value="${lastName}" required>
                        </div>
                        <div class="form-field">
                            <label for="new-age">Age (optional):</label>
                            <input type="number" id="new-age" min="1" max="120">
                        </div>
                        <div class="form-field">
                            <label for="new-housing-status">Housing Status:</label>
                            <select id="new-housing-status">
                                <option value="">Unknown</option>
                                <option value="Housed">Housed</option>
                                <option value="Homeless">Homeless</option>
                                <option value="At Risk">At Risk</option>
                                <option value="Transitional">Transitional</option>
                            </select>
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button type="button" class="action-btn" onclick="window.app.confirmCreatePerson()">CREATE PERSON</button>
                        <button type="button" class="action-btn" onclick="window.app.closeCreatePersonDialog()">CANCEL</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', dialogHtml);
    }

    async confirmCreatePerson() {
        const firstName = document.getElementById('new-first-name')?.value.trim();
        const lastName = document.getElementById('new-last-name')?.value.trim();
        const age = document.getElementById('new-age')?.value;
        const housingStatus = document.getElementById('new-housing-status')?.value;
        
        if (!firstName || !lastName) {
            this.uiUtilities.showToast('⚠️ Please provide first and last name', 'warning');
            return;
        }
        
        try {
            const personData = {
                first_name: firstName,
                last_name: lastName,
                age: age ? parseInt(age) : null,
                housing_status: housingStatus || null,
                created_by: this.auth.getCurrentUser()?.email || 'System',
                created_at: new Date().toISOString()
            };
            
            const newPerson = await this.data.insert('people', personData);
            
            if (newPerson) {
                this.closeCreatePersonDialog();
                this.uiUtilities.showToast('✅ Person created successfully', 'success');
                
                // Automatically add them to the incident
                this.showInvolvementTypeDialog(newPerson);
            }
            
        } catch (error) {
            console.error('Error creating person:', error);
            this.uiUtilities.showToast('❌ Error creating person', 'error');
        }
    }

    closeCreatePersonDialog() {
        const dialog = document.getElementById('create-person-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    formatMedicalIssueType(type) {
        const types = {
            'overdose': 'Overdose',
            'physical_injury': 'Physical Injury',
            'mental_health_crisis': 'Mental Health Crisis',
            'medical_emergency': 'Medical Emergency',
            'unconscious': 'Unconscious',
            'intoxication': 'Intoxication',
            'withdrawal': 'Withdrawal',
            'chronic_condition': 'Chronic Condition',
            'other': 'Other'
        };
        return types[type] || type;
    }

    showMedicalInfoDialog(personIndex) {
        if (personIndex < 0 || personIndex >= this.involvedPeople.length) {
            return;
        }

        const involvement = this.involvedPeople[personIndex];
        const medical = involvement.medical_info || {};
        const isUnknown = involvement.is_unknown_party;
        const person = involvement.person_data;
        const personName = isUnknown ? 'Unknown Person' : `${person?.first_name || 'Unknown'} ${person?.last_name || 'Person'}`;

        const medicalIssueTypes = [
            { value: '', label: 'Select type...' },
            { value: 'overdose', label: 'Overdose' },
            { value: 'physical_injury', label: 'Physical Injury' },
            { value: 'mental_health_crisis', label: 'Mental Health Crisis' },
            { value: 'medical_emergency', label: 'Medical Emergency' },
            { value: 'unconscious', label: 'Unconscious' },
            { value: 'intoxication', label: 'Intoxication' },
            { value: 'withdrawal', label: 'Withdrawal' },
            { value: 'chronic_condition', label: 'Chronic Condition' },
            { value: 'other', label: 'Other' }
        ];

        const firstAidTypes = [
            { value: 'naloxone', label: 'Naloxone Administered' },
            { value: 'wound_care', label: 'Wound Care' },
            { value: 'cpr', label: 'CPR' },
            { value: 'basic_first_aid', label: 'Basic First Aid' },
            { value: 'mental_health_support', label: 'Mental Health Support' },
            { value: 'positioning', label: 'Recovery Position' },
            { value: 'vital_signs', label: 'Vital Signs Check' },
            { value: 'other', label: 'Other' }
        ];

        const dialogHtml = `
            <div class="dialog-overlay" id="medical-info-dialog">
                <div class="dialog-content medical-dialog">
                    <div class="dialog-header">
                        <h3>Medical Information - ${personName}</h3>
                    </div>
                    <div class="dialog-body">
                        <div class="form-field">
                            <label>
                                <input type="checkbox" id="required-medical-attention" ${medical.required_medical_attention ? 'checked' : ''}> 
                                Required Medical Attention?
                            </label>
                        </div>

                        <div class="medical-details-section" id="medical-details-section" style="display: ${medical.required_medical_attention ? 'block' : 'none'}">
                            
                            <!-- Medical Issue Information -->
                            <div class="medical-subsection">
                                <h4>Medical Issue</h4>
                                <div class="form-field">
                                    <label for="medical-issue-type">Issue Type:</label>
                                    <select id="medical-issue-type">
                                        ${medicalIssueTypes.map(type => 
                                            `<option value="${type.value}" ${medical.medical_issue_type === type.value ? 'selected' : ''}>${type.label}</option>`
                                        ).join('')}
                                    </select>
                                </div>
                                <div class="form-field" id="medical-issue-other-field" style="display: ${medical.medical_issue_type === 'other' ? 'block' : 'none'}">
                                    <label for="medical-issue-other">Describe Other Issue:</label>
                                    <input type="text" id="medical-issue-other" value="${medical.medical_issue_other || ''}" placeholder="Specify the medical issue">
                                </div>
                                <div class="form-field">
                                    <label for="medical-issue-description">Detailed Description:</label>
                                    <textarea id="medical-issue-description" rows="3" placeholder="Provide details about the medical issue/injury">${medical.medical_issue_description || ''}</textarea>
                                </div>
                            </div>

                            <!-- First Aid Section -->
                            <div class="medical-subsection">
                                <h4>First Aid Provided by IHARC Staff</h4>
                                <div class="form-field">
                                    <label>
                                        <input type="checkbox" id="first-aid-provided" ${medical.first_aid_provided ? 'checked' : ''}> 
                                        First Aid Provided?
                                    </label>
                                </div>
                                <div class="first-aid-details" id="first-aid-details" style="display: ${medical.first_aid_provided ? 'block' : 'none'}">
                                    <div class="form-field">
                                        <label>First Aid Types:</label>
                                        <div class="checkbox-grid">
                                            ${firstAidTypes.map(type => `
                                                <label>
                                                    <input type="checkbox" name="first_aid_type" value="${type.value}" 
                                                           ${(medical.first_aid_type || []).includes(type.value) ? 'checked' : ''}> 
                                                    ${type.label}
                                                </label>
                                            `).join('')}
                                        </div>
                                    </div>
                                    <div class="form-field" id="first-aid-other-field" style="display: ${(medical.first_aid_type || []).includes('other') ? 'block' : 'none'}">
                                        <label for="first-aid-other">Describe Other First Aid:</label>
                                        <input type="text" id="first-aid-other" value="${medical.first_aid_other || ''}" placeholder="Specify other first aid provided">
                                    </div>
                                    <div class="form-field">
                                        <label for="first-aid-details-text">Additional First Aid Details:</label>
                                        <textarea id="first-aid-details-text" rows="2" placeholder="Additional details about first aid provided">${medical.first_aid_details || ''}</textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Transport Section -->
                            <div class="medical-subsection">
                                <h4>Hospital Transport</h4>
                                <div class="form-field">
                                    <label>
                                        <input type="checkbox" id="hospital-transport-offered" ${medical.hospital_transport_offered ? 'checked' : ''}> 
                                        Hospital Transport Offered?
                                    </label>
                                </div>
                                <div class="transport-details" id="transport-details" style="display: ${medical.hospital_transport_offered ? 'block' : 'none'}">
                                    <div class="form-field">
                                        <label>
                                            <input type="checkbox" id="transport-declined" ${medical.transport_declined ? 'checked' : ''}> 
                                            Transport Declined?
                                        </label>
                                    </div>
                                    <div class="form-field" id="transport-decline-reason-field" style="display: ${medical.transport_declined ? 'block' : 'none'}">
                                        <label for="transport-decline-reason">Reason for Declining:</label>
                                        <textarea id="transport-decline-reason" rows="2" placeholder="Why did they decline transport?">${medical.transport_decline_reason || ''}</textarea>
                                    </div>
                                    <div class="transport-accepted-details" id="transport-accepted-details" style="display: ${medical.hospital_transport_offered && !medical.transport_declined ? 'block' : 'none'}">
                                        <div class="form-field">
                                            <label for="hospital-destination">Hospital Destination:</label>
                                            <input type="text" id="hospital-destination" value="${medical.hospital_destination || ''}" placeholder="Which hospital?">
                                        </div>
                                        <div class="form-field">
                                            <label for="transported-by">Transported By:</label>
                                            <select id="transported-by">
                                                <option value="">Select...</option>
                                                <option value="paramedics" ${medical.transported_by === 'paramedics' ? 'selected' : ''}>Paramedics</option>
                                                <option value="police" ${medical.transported_by === 'police' ? 'selected' : ''}>Police</option>
                                                <option value="iharc_staff" ${medical.transported_by === 'iharc_staff' ? 'selected' : ''}>IHARC Staff</option>
                                                <option value="family" ${medical.transported_by === 'family' ? 'selected' : ''}>Family/Friend</option>
                                                <option value="other" ${medical.transported_by === 'other' ? 'selected' : ''}>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-field">
                                        <label for="transport-notes">Transport Notes:</label>
                                        <textarea id="transport-notes" rows="2" placeholder="Additional transport information">${medical.transport_notes || ''}</textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Assessment Section -->
                            <div class="medical-subsection">
                                <h4>Medical Assessment</h4>
                                <div class="form-field">
                                    <label for="injury-severity">Injury/Issue Severity:</label>
                                    <select id="injury-severity">
                                        <option value="">Select...</option>
                                        <option value="minor" ${medical.injury_severity === 'minor' ? 'selected' : ''}>Minor</option>
                                        <option value="moderate" ${medical.injury_severity === 'moderate' ? 'selected' : ''}>Moderate</option>
                                        <option value="severe" ${medical.injury_severity === 'severe' ? 'selected' : ''}>Severe</option>
                                        <option value="critical" ${medical.injury_severity === 'critical' ? 'selected' : ''}>Critical</option>
                                        <option value="unknown" ${medical.injury_severity === 'unknown' ? 'selected' : ''}>Unknown</option>
                                    </select>
                                </div>
                                <div class="form-field">
                                    <label for="consciousness-level">Consciousness Level:</label>
                                    <select id="consciousness-level">
                                        <option value="">Select...</option>
                                        <option value="alert" ${medical.consciousness_level === 'alert' ? 'selected' : ''}>Alert</option>
                                        <option value="drowsy" ${medical.consciousness_level === 'drowsy' ? 'selected' : ''}>Drowsy</option>
                                        <option value="unconscious" ${medical.consciousness_level === 'unconscious' ? 'selected' : ''}>Unconscious</option>
                                        <option value="responsive_to_voice" ${medical.consciousness_level === 'responsive_to_voice' ? 'selected' : ''}>Responsive to Voice</option>
                                        <option value="responsive_to_pain" ${medical.consciousness_level === 'responsive_to_pain' ? 'selected' : ''}>Responsive to Pain</option>
                                        <option value="unresponsive" ${medical.consciousness_level === 'unresponsive' ? 'selected' : ''}>Unresponsive</option>
                                    </select>
                                </div>
                                <div class="form-field">
                                    <label for="medical-assessment-notes">Assessment Notes:</label>
                                    <textarea id="medical-assessment-notes" rows="3" placeholder="Detailed medical assessment notes">${medical.medical_assessment_notes || ''}</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="dialog-actions">
                        <button type="button" class="action-btn" onclick="window.app.saveMedicalInfo(${personIndex})">SAVE MEDICAL INFO</button>
                        <button type="button" class="action-btn" onclick="window.app.closeMedicalInfoDialog()">CANCEL</button>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', dialogHtml);

        // Set up event handlers for conditional fields
        this.setupMedicalDialogHandlers();
    }

    setupMedicalDialogHandlers() {
        // Required medical attention checkbox
        const medicalCheckbox = document.getElementById('required-medical-attention');
        const medicalDetailsSection = document.getElementById('medical-details-section');
        
        if (medicalCheckbox && medicalDetailsSection) {
            medicalCheckbox.addEventListener('change', () => {
                medicalDetailsSection.style.display = medicalCheckbox.checked ? 'block' : 'none';
            });
        }

        // Medical issue type dropdown
        const medicalIssueType = document.getElementById('medical-issue-type');
        const medicalIssueOtherField = document.getElementById('medical-issue-other-field');
        
        if (medicalIssueType && medicalIssueOtherField) {
            medicalIssueType.addEventListener('change', () => {
                medicalIssueOtherField.style.display = medicalIssueType.value === 'other' ? 'block' : 'none';
            });
        }

        // First aid provided checkbox
        const firstAidCheckbox = document.getElementById('first-aid-provided');
        const firstAidDetails = document.getElementById('first-aid-details');
        
        if (firstAidCheckbox && firstAidDetails) {
            firstAidCheckbox.addEventListener('change', () => {
                firstAidDetails.style.display = firstAidCheckbox.checked ? 'block' : 'none';
            });
        }

        // First aid "other" type checkbox
        const firstAidTypeCheckboxes = document.querySelectorAll('input[name="first_aid_type"]');
        const firstAidOtherField = document.getElementById('first-aid-other-field');
        
        if (firstAidTypeCheckboxes.length && firstAidOtherField) {
            firstAidTypeCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    const otherChecked = Array.from(firstAidTypeCheckboxes).some(cb => cb.value === 'other' && cb.checked);
                    firstAidOtherField.style.display = otherChecked ? 'block' : 'none';
                });
            });
        }

        // Hospital transport offered checkbox
        const transportOfferedCheckbox = document.getElementById('hospital-transport-offered');
        const transportDetails = document.getElementById('transport-details');
        
        if (transportOfferedCheckbox && transportDetails) {
            transportOfferedCheckbox.addEventListener('change', () => {
                transportDetails.style.display = transportOfferedCheckbox.checked ? 'block' : 'none';
            });
        }

        // Transport declined checkbox
        const transportDeclinedCheckbox = document.getElementById('transport-declined');
        const transportDeclineReasonField = document.getElementById('transport-decline-reason-field');
        const transportAcceptedDetails = document.getElementById('transport-accepted-details');
        
        if (transportDeclinedCheckbox) {
            transportDeclinedCheckbox.addEventListener('change', () => {
                if (transportDeclineReasonField) {
                    transportDeclineReasonField.style.display = transportDeclinedCheckbox.checked ? 'block' : 'none';
                }
                if (transportAcceptedDetails) {
                    transportAcceptedDetails.style.display = transportDeclinedCheckbox.checked ? 'none' : 'block';
                }
            });
        }
    }

    saveMedicalInfo(personIndex) {
        if (personIndex < 0 || personIndex >= this.involvedPeople.length) {
            return;
        }

        // Collect medical information from form
        const requiredAttention = document.getElementById('required-medical-attention')?.checked || false;
        
        const medicalInfo = {
            required_medical_attention: requiredAttention
        };

        if (requiredAttention) {
            // Medical issue info
            medicalInfo.medical_issue_type = document.getElementById('medical-issue-type')?.value || null;
            medicalInfo.medical_issue_other = document.getElementById('medical-issue-other')?.value || null;
            medicalInfo.medical_issue_description = document.getElementById('medical-issue-description')?.value || null;

            // First aid info
            medicalInfo.first_aid_provided = document.getElementById('first-aid-provided')?.checked || false;
            if (medicalInfo.first_aid_provided) {
                const firstAidTypes = Array.from(document.querySelectorAll('input[name="first_aid_type"]:checked'))
                    .map(cb => cb.value);
                medicalInfo.first_aid_type = firstAidTypes;
                medicalInfo.first_aid_other = document.getElementById('first-aid-other')?.value || null;
                medicalInfo.first_aid_details = document.getElementById('first-aid-details-text')?.value || null;
            }

            // Transport info
            medicalInfo.hospital_transport_offered = document.getElementById('hospital-transport-offered')?.checked || false;
            if (medicalInfo.hospital_transport_offered) {
                medicalInfo.transport_declined = document.getElementById('transport-declined')?.checked || false;
                medicalInfo.transport_decline_reason = document.getElementById('transport-decline-reason')?.value || null;
                medicalInfo.hospital_destination = document.getElementById('hospital-destination')?.value || null;
                medicalInfo.transported_by = document.getElementById('transported-by')?.value || null;
                medicalInfo.transport_notes = document.getElementById('transport-notes')?.value || null;
            }

            // Assessment info
            medicalInfo.injury_severity = document.getElementById('injury-severity')?.value || null;
            medicalInfo.consciousness_level = document.getElementById('consciousness-level')?.value || null;
            medicalInfo.medical_assessment_notes = document.getElementById('medical-assessment-notes')?.value || null;
        }

        // Update the person's medical info
        this.involvedPeople[personIndex].medical_info = medicalInfo;
        
        // Update the display
        this.updateInvolvedPeopleDisplay();
        
        // Close the dialog
        this.closeMedicalInfoDialog();
        
        this.uiUtilities.showToast('✅ Medical information saved', 'success');
    }

    closeMedicalInfoDialog() {
        const dialog = document.getElementById('medical-info-dialog');
        if (dialog) {
            dialog.remove();
        }
    }

    // saveIncidentPeopleRelationships() and updateIncidentPeopleRelationships() methods removed - now handled by incident-people-manager.js

    async deleteIncidentPeopleRelationships(incidentId) {
        try {
            console.log(`🗑️ Deleting existing people relationships for incident ${incidentId}`);
            
            // First check what exists and delete corresponding activities
            const supabase = await this.data.getSupabaseClient();
            if (supabase) {
                // Check existing relationships before deletion
                const checkResult = await supabase
                    .schema('case_mgmt')
                    .from('incident_people')
                    .select('*')
                    .eq('incident_id', incidentId);
                    
                if (checkResult.data) {
                    console.log(`📋 Found ${checkResult.data.length} existing relationships to delete:`, checkResult.data);
                    
                    // Delete corresponding incident activities for each person
                    for (const relationship of checkResult.data) {
                        if (relationship.person_id && !relationship.is_unknown_party) {
                            await this.deleteIncidentActivity(incidentId, relationship.person_id);
                        }
                    }
                }
                
                // Delete from Supabase
                const deleteResult = await supabase
                    .schema('case_mgmt')
                    .from('incident_people')
                    .delete()
                    .eq('incident_id', incidentId);
                    
                if (deleteResult.error) {
                    console.error('❌ Supabase delete error:', deleteResult.error);
                    throw deleteResult.error;
                } else {
                    console.log('✅ Deleted existing relationships from Supabase');
                }
            }
            
            // Delete from local cache
            if (this.data.sqlite && this.data.sqlite.db) {
                const cacheResult = this.data.sqlite.db.prepare('DELETE FROM cache_incident_people WHERE incident_id = ?').run(incidentId);
                console.log(`✅ Deleted ${cacheResult.changes} relationships from cache`);
            }
            
        } catch (error) {
            console.error('❌ Error deleting existing relationships:', error);
            throw error; // Now throw the error so we know if deletion failed
        }
    }

    async createIncidentPersonRelationship(relationshipData) {
        try {
            let supabaseResult = null;
            
            // First try to create in Supabase
            const supabase = await this.data.getSupabaseClient();
            if (supabase) {
                supabaseResult = await supabase
                    .schema('case_mgmt')
                    .from('incident_people')
                    .insert(relationshipData)
                    .select()
                    .single();

                if (supabaseResult.error) {
                    throw supabaseResult.error;
                }
            }

            // Then cache locally
            if (this.data.sqlite && this.data.sqlite.db) {
                this.data.sqlite.db.prepare(`
                    INSERT INTO cache_incident_people (
                        incident_id, person_id, involvement_type, is_unknown_party,
                        unknown_party_description, notes, created_at, created_by,
                        cached_at, sync_status, cache_version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'synced', 1)
                `).run(
                    relationshipData.incident_id,
                    relationshipData.person_id,
                    relationshipData.involvement_type,
                    relationshipData.is_unknown_party ? 1 : 0,
                    relationshipData.unknown_party_description,
                    relationshipData.notes,
                    relationshipData.created_at,
                    relationshipData.created_by
                );
            }

            return supabaseResult?.data || null;

        } catch (error) {
            console.error('Error creating incident person relationship:', error);
            throw error;
        }
    }

    async createIncidentPersonMedical(incidentPersonId, medicalInfo) {
        try {
            const medicalData = {
                incident_person_id: incidentPersonId,
                required_medical_attention: medicalInfo.required_medical_attention || false,
                medical_issue_type: medicalInfo.medical_issue_type || null,
                medical_issue_description: medicalInfo.medical_issue_description || null,
                medical_issue_other: medicalInfo.medical_issue_other || null,
                first_aid_provided: medicalInfo.first_aid_provided || false,
                first_aid_type: Array.isArray(medicalInfo.first_aid_type) && medicalInfo.first_aid_type.length > 0 
                    ? medicalInfo.first_aid_type 
                    : null,
                first_aid_other: medicalInfo.first_aid_other || null,
                first_aid_details: medicalInfo.first_aid_details || null,
                hospital_transport_offered: medicalInfo.hospital_transport_offered || false,
                transport_declined: medicalInfo.transport_declined || false,
                transport_decline_reason: medicalInfo.transport_decline_reason || null,
                hospital_destination: medicalInfo.hospital_destination || null,
                transported_by: medicalInfo.transported_by || null,
                transport_notes: medicalInfo.transport_notes || null,
                injury_severity: medicalInfo.injury_severity || null,
                consciousness_level: medicalInfo.consciousness_level || null,
                medical_assessment_notes: medicalInfo.medical_assessment_notes || null,
                created_by: this.auth.getCurrentUser()?.email || 'System',
                created_at: new Date().toISOString()
            };

            // First create in Supabase
            let supabaseResult = null;
            const supabase = await this.data.getSupabaseClient();
            if (supabase) {
                supabaseResult = await supabase
                    .schema('case_mgmt')
                    .from('incident_person_medical')
                    .insert(medicalData)
                    .select()
                    .single();

                if (supabaseResult.error) {
                    throw supabaseResult.error;
                }
            }

            // Then cache locally
            if (this.data.sqlite && this.data.sqlite.db) {
                this.data.sqlite.db.prepare(`
                    INSERT INTO cache_incident_person_medical (
                        incident_person_id, required_medical_attention, medical_issue_type,
                        medical_issue_description, medical_issue_other, first_aid_provided,
                        first_aid_type, first_aid_other, first_aid_details,
                        hospital_transport_offered, transport_declined, transport_decline_reason,
                        hospital_destination, transported_by, transport_notes,
                        injury_severity, consciousness_level, medical_assessment_notes,
                        created_at, created_by, cached_at, sync_status, cache_version
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'synced', 1)
                `).run(
                    medicalData.incident_person_id,
                    medicalData.required_medical_attention ? 1 : 0,
                    medicalData.medical_issue_type,
                    medicalData.medical_issue_description,
                    medicalData.medical_issue_other,
                    medicalData.first_aid_provided ? 1 : 0,
                    JSON.stringify(medicalData.first_aid_type),
                    medicalData.first_aid_other,
                    medicalData.first_aid_details,
                    medicalData.hospital_transport_offered ? 1 : 0,
                    medicalData.transport_declined ? 1 : 0,
                    medicalData.transport_decline_reason,
                    medicalData.hospital_destination,
                    medicalData.transported_by,
                    medicalData.transport_notes,
                    medicalData.injury_severity,
                    medicalData.consciousness_level,
                    medicalData.medical_assessment_notes,
                    medicalData.created_at,
                    medicalData.created_by
                );
            }

            return supabaseResult?.data || null;

        } catch (error) {
            console.error('Error creating incident person medical record:', error);
            throw error;
        }
    }

    async createIncidentActivity(incidentId, personId, involvementType) {
        try {
            // Get incident details to create proper activity description
            const incident = await this.data.get('incidents', incidentId);
            if (!incident) {
                console.warn(`Incident ${incidentId} not found, skipping activity creation`);
                return;
            }

            // Get current user info
            const currentUser = this.auth.getCurrentUser();
            const staffMember = currentUser?.name || currentUser?.email || 'System';

            // Create activity data
            const activityData = {
                person_id: personId,
                activity_type: 'incident',
                title: 'incident',
                description: `Involved in incident ${incident.incident_number || incidentId} as ${involvementType}. ${incident.description || incident.narrative || ''}`.trim(),
                location: incident.location || incident.street_address || null,
                coordinates: incident.coordinates || null,
                activity_date: incident.incident_date || new Date().toISOString().split('T')[0],
                activity_time: incident.incident_time || null,
                staff_member: staffMember,
                outcome: `Incident involvement: ${involvementType}`,
                tags: ['incident', involvementType],
                attachments: JSON.stringify({
                    incident_id: incidentId,
                    incident_number: incident.incident_number,
                    link_type: 'incident_involvement'
                }),
                created_by: currentUser?.email || 'System',
                created_at: new Date().toISOString()
            };

            // Insert the activity
            const activity = await this.data.insert('people_activities', activityData);
            console.log(`✅ Created incident activity for person ${personId}: ${activity.id}`);
            
            return activity;

        } catch (error) {
            console.error(`❌ Error creating incident activity for person ${personId}:`, error);
            // Don't throw error - this is a supplementary feature, shouldn't break incident creation
        }
    }

    async deleteIncidentActivity(incidentId, personId) {
        try {
            console.log(`🗑️ Deleting incident activity for person ${personId} from incident ${incidentId}`);
            
            // Find activities that are linked to this specific incident
            const activities = await this.data.search('people_activities', { 
                person_id: personId,
                activity_type: 'incident'
            });
            
            // Filter to find the specific activity for this incident
            const activityToDelete = activities.find(activity => {
                if (!activity.attachments) return false;
                
                try {
                    const attachments = typeof activity.attachments === 'string' 
                        ? JSON.parse(activity.attachments) 
                        : activity.attachments;
                    
                    return attachments && 
                           attachments.link_type === 'incident_involvement' && 
                           attachments.incident_id === incidentId;
                } catch (e) {
                    return false;
                }
            });
            
            if (activityToDelete) {
                // Delete the activity
                await this.data.delete('people_activities', activityToDelete.id);
                console.log(`✅ Deleted incident activity ${activityToDelete.id} for person ${personId}`);
                
                // Refresh person activities if they are currently viewing this person
                if (this.selectedPerson && this.selectedPerson.id == personId) {
                    await this.loadPersonActivities(personId);
                }
            } else {
                console.log(`📝 No incident activity found for person ${personId} and incident ${incidentId}`);
            }
            
        } catch (error) {
            console.error(`❌ Error deleting incident activity for person ${personId}:`, error);
            // Don't throw error - this is cleanup, shouldn't break main flow
        }
    }

    // loadIncidentPeopleRelationships() method removed - now handled by incident-people-manager.js

    // loadExistingIncidentPeople() method removed - now handled by incident-people-manager.js

    // setupEditIncidentForm() method removed - now handled by incident-form-manager.js

    // handleEditIncidentFormSubmission() method removed - now handled by incident-form-manager.js


    setupIncidentFormTabs() {
        console.log('Setting up incident form tabs...');
        const tabs = document.querySelectorAll('.incident-form-tab');
        const panels = document.querySelectorAll('.tab-content');

        console.log('Found tabs:', tabs.length, 'Found panels:', panels.length);

        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = tab.getAttribute('data-tab');
                console.log('Tab clicked:', targetTab);

                // Remove active class from all tabs
                tabs.forEach(t => t.classList.remove('active'));
                // Add active class to clicked tab
                tab.classList.add('active');

                // Hide all panels
                panels.forEach(panel => {
                    panel.classList.remove('active');
                    panel.style.display = 'none';
                });

                // Show target panel - look for panel with matching data-tab attribute
                const targetPanel = document.querySelector(`.tab-content[data-tab="${targetTab}"]`);
                if (targetPanel) {
                    targetPanel.classList.add('active');
                    targetPanel.style.display = 'block';
                    console.log('Showing panel:', targetTab);

                    // Initialize narrative management if narrative tab is selected
                    if (targetTab === 'narrative' && this.narrativeManagement) {
                        setTimeout(() => {
                            // Check if we're editing an existing incident
                            if (this.editingIncidentId) {
                                // Load incident data for editing
                                this.data.get('incidents', this.editingIncidentId).then(incident => {
                                    if (incident) {
                                        this.narrativeManagement.loadFromIncident(incident);
                                        this.narrativeManagement.init(incident.id, this.narrativeManagement.getEntries());
                                    }
                                });
                            } else {
                                // Initialize for new incident (no ID)
                                this.narrativeManagement.init(null, []);
                            }
                        }, 100);
                    }
                } else {
                    console.error('Panel not found for tab:', targetTab);
                }
            });
        });

        // Initialize - show first tab
        if (tabs.length > 0 && panels.length > 0) {
            panels.forEach(panel => {
                panel.style.display = 'none';
                panel.classList.remove('active');
            });
            
            const firstPanel = document.querySelector('.tab-content[data-tab="basic"]');
            if (firstPanel) {
                firstPanel.style.display = 'block';
                firstPanel.classList.add('active');
            }
        }
    }

    async setupGoogleMapsAddressSearch() {
        console.log('Setting up Google Maps address search...');

        const container = document.getElementById('address-search-container');
        if (!container) {
            console.error('Address search container not found');
            return;
        }

        try {
            // Initialize Google Maps if not already done
            const initialized = await this.googleMapsAddress.initialize();
            if (!initialized) {
                console.error('Failed to initialize Google Maps');
                this.showAddressSearchFallback(container);
                return;
            }

            // Create the address search field
            const addressInput = this.googleMapsAddress.createAddressSearchField(
                container,
                {
                    placeholder: 'Start typing an address...',
                    className: 'address-search-input',
                    id: 'address_search',
                    required: true
                },
                (addressData) => {
                    this.handleAddressSelection(addressData);
                }
            );

            // Google Maps address search initialized

        } catch (error) {
            console.error('Error setting up Google Maps address search:', error);
            this.showAddressSearchFallback(container);
        }
    }

    showAddressSearchFallback(container) {
        console.log('Showing address search fallback');
        container.innerHTML = `
            <input type="text" id="address_search" class="address-search-input"
                   placeholder="Enter address manually" required>
            <small class="field-help error">Google Maps not available - please enter address manually</small>
        `;

        // Make visible address fields editable for manual entry
        const editableFields = ['street_address', 'city'];
        editableFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.removeAttribute('readonly');
                field.required = true;
            }
        });

        // Ensure hidden fields have default values
        const provinceField = document.getElementById('province');
        const countryField = document.getElementById('country');
        if (provinceField) provinceField.value = 'Ontario';
        if (countryField) countryField.value = 'Canada';
    }

    handleAddressSelection(addressData) {
        console.log('Address selected:', addressData);

        // Populate visible form fields
        const visibleFieldMappings = {
            'street_address': addressData.street_address,
            'city': addressData.city,
            'location': addressData.formatted_address
        };

        Object.entries(visibleFieldMappings).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field && value) {
                field.value = value;
            }
        });

        // Populate hidden fields (always set to Ontario, Canada)
        const provinceField = document.getElementById('province');
        const countryField = document.getElementById('country');
        const postalCodeField = document.getElementById('postal_code');

        if (provinceField) provinceField.value = 'Ontario';
        if (countryField) countryField.value = 'Canada';
        if (postalCodeField && addressData.postal_code) {
            postalCodeField.value = addressData.postal_code;
        }

        // Handle coordinates
        if (addressData.coordinates) {
            const coordinatesField = document.getElementById('coordinates');
            if (coordinatesField) {
                coordinatesField.value = `${addressData.coordinates.lat},${addressData.coordinates.lng}`;
            }
        }

        console.log('Address fields populated successfully');
    }

    // setupIncidentAddressSearch() method removed - now handled by incident-address-manager.js

    addGoogleMapsIndicator(addressInput) {
        // Add a small indicator that Google Maps is active
        const indicator = document.createElement('span');
        indicator.className = 'google-maps-indicator';
        indicator.innerHTML = '🗺️';
        indicator.title = 'Google Maps address search is active';
        indicator.style.cssText = `
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 12px;
            color: #4CAF50;
            pointer-events: none;
            z-index: 10;
        `;

        // Make the input container relative if it isn't already
        const container = addressInput.parentNode;
        if (container.style.position !== 'relative') {
            container.style.position = 'relative';
        }

        container.appendChild(indicator);
    }

    setupManualAddressEntry(addressInput) {
        console.log('Setting up manual address entry fallback');

        // Update the help text to indicate manual entry
        const helpText = addressInput.parentNode.querySelector('.field-help');
        if (helpText) {
            helpText.textContent = 'Google Maps not available - please enter address manually in the fields below';
            helpText.style.color = '#e74c3c';
        }

        // Show the address components section
        const addressComponents = document.querySelector('.address-components');
        if (addressComponents) {
            addressComponents.style.display = 'block';

            // Make the fields required
            const requiredFields = ['street_address', 'city'];
            requiredFields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) {
                    field.required = true;
                }
            });
        }

        // Set up a listener to populate the main location field when manual fields change
        const manualFields = ['street_address', 'city', 'province', 'postal_code'];
        manualFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('input', () => {
                    this.updateLocationFromManualFields();
                });
            }
        });
    }

    updateLocationFromManualFields() {
        const streetAddress = document.getElementById('street_address')?.value || '';
        const city = document.getElementById('city')?.value || '';
        const province = document.getElementById('province')?.value || '';
        const postalCode = document.getElementById('postal_code')?.value || '';

        // Build the location string
        const locationParts = [streetAddress, city, province, postalCode].filter(part => part.trim());
        const location = locationParts.join(', ');

        // Update the hidden location field and address_search field
        const locationField = document.getElementById('location');
        const addressSearchField = document.getElementById('address_search');

        if (locationField) {
            locationField.value = location;
        }
        if (addressSearchField && location) {
            addressSearchField.value = location;
        }
    }

    // handleIncidentAddressSelection() method removed - now handled by incident-address-manager.js

    async setupFoundLocationAutocomplete() {
        console.log('Setting up Google Maps autocomplete for found location...');

        const container = document.getElementById('found-location-search-container');
        const foundLocationInput = document.getElementById('found_location');
        
        if (!container || !foundLocationInput) {
            console.warn('Found location container or input not found');
            return;
        }

        try {
            // Initialize Google Maps if not already done
            const initialized = await this.googleMapsAddress.initialize();
            if (!initialized) {
                console.error('Failed to initialize Google Maps for found location');
                this.showFoundLocationFallback(foundLocationInput);
                return;
            }

            // Set up address autocomplete on the found location input
            this.googleMapsAddress.setupAddressSearchListeners(
                foundLocationInput,
                null, // No suggestions container needed for simple autocomplete
                (addressData) => {
                    console.log('Found location address selected:', addressData);
                    this.handleFoundLocationSelection(addressData, foundLocationInput);
                }
            );

            // Add visual indicator that Google Maps is working
            this.addGoogleMapsIndicator(foundLocationInput);

            console.log('Found location Google Maps autocomplete initialized');

        } catch (error) {
            console.error('Error setting up found location autocomplete:', error);
            this.showFoundLocationFallback(foundLocationInput);
        }
    }

    showFoundLocationFallback(foundLocationInput) {
        console.log('Showing found location manual entry fallback');
        foundLocationInput.placeholder = 'Enter location manually (Google Maps not available)';
        foundLocationInput.style.borderColor = '#e74c3c';
    }

    handleFoundLocationSelection(addressData, foundLocationInput) {
        console.log('Found location address selected:', addressData);
        
        // Simply populate the found location field with the formatted address
        if (foundLocationInput && addressData.formatted_address) {
            foundLocationInput.value = addressData.formatted_address;
            
            // Store coordinates if available (for future use)
            if (addressData.coordinates) {
                foundLocationInput.dataset.coordinates = `${addressData.coordinates.lat},${addressData.coordinates.lng}`;
            }
            
            // Trigger change event for any listeners
            foundLocationInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        console.log('Found location populated successfully');
    }

    setupFileUpload() {
        const uploadZone = document.getElementById('upload-zone');
        const fileInput = document.getElementById('file-input');
        const browseButton = document.getElementById('browse-files');
        const uploadedFilesContainer = document.getElementById('uploaded-files');

        console.log('Setting up file upload...', {
            uploadZone: !!uploadZone,
            fileInput: !!fileInput,
            browseButton: !!browseButton,
            uploadedFilesContainer: !!uploadedFilesContainer
        });

        if (!uploadZone || !fileInput || !browseButton) {
            console.error('File upload elements not found');
            return;
        }

        this.uploadedFiles = [];

        // Handle browse button click
        browseButton.addEventListener('click', () => {
            fileInput.click();
        });

        // Handle file input change
        fileInput.addEventListener('change', (e) => {
            this.handleFileSelection(Array.from(e.target.files));
        });

        // Handle drag and drop
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('drag-over');
        });

        uploadZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
        });

        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('drag-over');
            const files = Array.from(e.dataTransfer.files);
            this.handleFileSelection(files);
        });
    }

    handleFileSelection(files) {
        const uploadedFilesContainer = document.getElementById('uploaded-files');
        if (!uploadedFilesContainer) return;

        files.forEach(file => {
            // Add file to uploaded files array
            const fileData = {
                id: this.generateFileId(),
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                status: 'pending'
            };

            this.uploadedFiles.push(fileData);

            // Create file preview element
            const fileElement = this.createFilePreviewElement(fileData);
            uploadedFilesContainer.appendChild(fileElement);
        });

        // Show uploaded files container if hidden
        if (this.uploadedFiles.length > 0) {
            uploadedFilesContainer.style.display = 'block';
        }
    }

    createFilePreviewElement(fileData) {
        const fileElement = document.createElement('div');
        fileElement.className = 'uploaded-file';
        fileElement.dataset.fileId = fileData.id;

        const isImage = fileData.type.startsWith('image/');
        const fileIcon = this.getFileIcon(fileData.type);

        fileElement.innerHTML = `
            <div class="file-preview">
                ${isImage ? `<img src="${URL.createObjectURL(fileData.file)}" alt="${fileData.name}" class="file-thumbnail">` : `<div class="file-icon">${fileIcon}</div>`}
            </div>
            <div class="file-info">
                <div class="file-name">${fileData.name}</div>
                <div class="file-size">${this.formatFileSize(fileData.size)}</div>
                <div class="file-status" id="status-${fileData.id}">Ready to upload</div>
            </div>
            <button type="button" class="remove-file-btn" data-file-id="${fileData.id}">×</button>
        `;

        // Handle file removal
        const removeButton = fileElement.querySelector('.remove-file-btn');
        removeButton.addEventListener('click', () => {
            this.removeFile(fileData.id);
        });

        return fileElement;
    }

    removeFile(fileId) {
        // Remove from uploaded files array
        this.uploadedFiles = this.uploadedFiles.filter(file => file.id !== fileId);

        // Remove from DOM
        const fileElement = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileElement) {
            fileElement.remove();
        }

        // Hide container if no files
        const uploadedFilesContainer = document.getElementById('uploaded-files');
        if (this.uploadedFiles.length === 0 && uploadedFilesContainer) {
            uploadedFilesContainer.style.display = 'none';
        }
    }

    getFileIcon(mimeType) {
        if (mimeType.startsWith('image/')) return '🖼️';
        if (mimeType === 'application/pdf') return '📄';
        if (mimeType.startsWith('video/')) return '🎥';
        if (mimeType.startsWith('audio/')) return '🎵';
        if (mimeType.includes('word') || mimeType.includes('document')) return '📝';
        if (mimeType.includes('excel') || mimeType.includes('spreadsheet')) return '📊';
        return '📁';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    generateFileId() {
        return 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    async handleIncidentFormSubmission() {
        // Prevent duplicate submissions
        if (this.isSubmittingIncident) {
            console.log('⚠️ Incident submission already in progress, ignoring duplicate');
            return;
        }

        this.isSubmittingIncident = true;

        const form = document.getElementById('create-incident-form');
        const submitButton = document.getElementById('submit-incident');

        if (!form) {
            this.isSubmittingIncident = false;
            return;
        }

        try {
            // Disable submit button
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'Creating Incident...';
            }

            // Collect form data
            const formData = new FormData(form);
            const incidentData = {};

            // Process form fields (exclude UI-only fields)
            for (let [key, value] of formData.entries()) {
                // Skip UI-only fields that don't belong in the database
                if (key === 'use_current_time') {
                    continue;
                }

                if (key === 'police_notified') {
                    incidentData[key] = form.querySelector(`[name="${key}"]`).checked;
                } else {
                    incidentData[key] = value;
                }
            }

            // incident_number is generated by database trigger
            incidentData.reported_by = this.auth.getCurrentUser()?.email;
            incidentData.status = 'open';
            incidentData.created_at = new Date().toISOString();

            // Set incident date/time
            if (form.querySelector('[name="use_current_time"]').checked) {
                const now = new Date();
                incidentData.incident_date = now.toISOString().split('T')[0];
                incidentData.incident_time = now.toTimeString().split(' ')[0];
            }

            // Transform form data to match Supabase schema
            const dbIncidentData = {
                // Basic fields
                location: incidentData.location,
                narrative: incidentData.description, // Map description to narrative
                description: incidentData.description, // Also store in description field for dispatch
                incident_number: incidentData.incident_number,
                reporter_id: null, // Will be set by RLS policy or trigger

                // Dispatch-specific fields
                incident_date: incidentData.incident_date,
                incident_time: incidentData.incident_time,
                incident_type: incidentData.incident_type,
                priority: incidentData.priority || 'medium',
                status: incidentData.status || 'open',
                assigned_ranger: incidentData.assigned_ranger || null,
                coordinates: incidentData.coordinates || null,
                reported_by: incidentData.reported_by,
                dispatch_notes: incidentData.dispatch_notes || null,

                // Address components (for future use and better data structure)
                street_address: incidentData.street_address || null,
                city: incidentData.city || null,
                province: incidentData.province || null,
                postal_code: incidentData.postal_code || null,
                location_notes: incidentData.location_notes || null,

                // Additional fields
                is_urgent: incidentData.is_urgent || false,
                tags: incidentData.tags ? (Array.isArray(incidentData.tags) ? incidentData.tags : [incidentData.tags]) : []
            };

            // Save incident to database
            const result = await this.data.insert('incidents', dbIncidentData);
            const incidentId = result.id;

            // Upload files if any
            if (this.uploadedFiles && this.uploadedFiles.length > 0) {
                await this.incidentManagement.fileManager.uploadIncidentFiles(incidentId);
            }

            // Save linked records - now handled by incident management module
            // await this.incidentManagement.crudManager.saveIncidentCreationLinks(incidentId, this.incidentCreationLinks);

            // Save people relationships if any were added
            if (this.involvedPeople && this.involvedPeople.length > 0) {
                console.log(`Saving ${this.involvedPeople.length} people relationships for incident ${incidentId}`);
                await this.incidentManagement.peopleManager.saveIncidentPeopleRelationships(incidentId, this.involvedPeople);
            }

            // Refresh dispatch if it's currently active
            if (this.currentTab === 'dispatch') {
                this.loadDispatchIncidents();
            }

            // Emit data change event for any listeners
            if (this.data && this.data.emitDataChange) {
                this.data.emitDataChange('incidents', 'create', result);
            }

            // Show success message with dispatch option
            const dialogResult = await this.ui.showDialog(
                'Incident Created',
                `Incident ${incidentData.incident_number} has been successfully created.`,
                'success',
                {
                    showCancel: true,
                    confirmText: 'View in Dispatch',
                    cancelText: 'Back to Incidents'
                }
            );

            // Navigate based on user choice
            if (dialogResult === true) {
                // User chose to view in dispatch
                this.goToDispatch(incidentId);
            } else {
                // Navigate back to incidents
                this.loadTabContent('incidents');
            }

        } catch (error) {
            console.error('Error creating incident:', error);
            this.ui.showDialog('Error', `Failed to create incident: ${error.message}`, 'error');
        } finally {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.textContent = 'Create Incident Report';

            // Reset submission flag
            this.isSubmittingIncident = false;
        }
    }

    // saveIncidentCreationLinks() method removed - now handled by incident-crud-manager.js

    // uploadIncidentFiles() method removed - now handled by incident-file-manager.js

    // generateIncidentNumber() method removed - incident numbers are generated by database trigger

    // loadIncidentAttachments() method removed - now handled by incident-file-manager.js

    // downloadIncidentAttachment(), generateAttachmentsHTML(), getFileIcon(), formatFileSize() methods removed - now handled by incident-file-manager.js

    async loadRecordsContent() {
        const { RecordsTemplates } = await import('../../modules/records/index.js');
        return RecordsTemplates.getMainView();
    }


    async refreshPeopleList() {
        console.log(`🔄 refreshPeopleList called. Current screen: ${this.currentScreen}`);

        // Only refresh if we're currently on the people management screen
        if (this.currentScreen !== 'people-management') {
            console.log(`⏭️ Skipping people list refresh - not on people-management screen (current: ${this.currentScreen})`);
            return;
        }

        console.log('🔄 Refreshing people list...');

        try {
            const people = await this.data.search('people', {});
            console.log(`📊 Retrieved ${people.length} people for refresh:`, people.map(p => `${p.first_name} ${p.last_name} (${p.id})`));

            const peopleList = document.getElementById('people-list');
            const peopleCount = document.getElementById('people-count');

            console.log('🎯 DOM elements found:', {
                peopleList: !!peopleList,
                peopleCount: !!peopleCount
            });

            if (peopleList) {
                const renderedHTML = this.renderPeopleList(people);
                console.log('📝 Rendered HTML length:', renderedHTML.length);
                peopleList.innerHTML = renderedHTML;
            }

            if (peopleCount) {
                peopleCount.textContent = people.length;
            }

            console.log('✅ People list refreshed with', people.length, 'records');
        } catch (error) {
            console.error('❌ Error refreshing people list:', error);
        }
    }

    // ===============================
    // ADDRESS MANAGEMENT METHODS
    // ===============================

    async loadAddressesManagementContent() {
        const contentArea = document.getElementById('content-area');
        if (contentArea) {
            const content = await this.addressManager.loadAddressesManagementContent();
            contentArea.innerHTML = content;
        }
    }

    async viewAddressDetail(address) {
        const contentArea = document.getElementById('content-area');
        if (contentArea) {
            const content = await this.addressManager.showAddressDetailView(address);
            contentArea.innerHTML = content;
        }
    }

    async viewPersonDetail(personId) {
        try {
            const person = await this.data.get('people', personId);
            if (!person) {
                this.ui.showDialog('Error', 'Person not found', 'error');
                return;
            }

            this.selectedPerson = person;
            await this.loadTabContent('records', 'person-detail');
        } catch (error) {
            console.error('Error viewing person detail:', error);
            this.ui.showDialog('Error', 'Failed to load person details', 'error');
        }
    }

    async loadOrganizationsManagementContent() {
        const organizations = await this.data.search('organizations', {});

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>ORGANIZATION MANAGEMENT</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-organization">
                            <span class="button-icon">🏢</span>
                            Add Organization
                        </button>
                        <button class="secondary-button" data-action="back-to-records">
                            <span class="button-icon">←</span>
                            Back to Records
                        </button>
                    </div>
                </div>

                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="organizations-search" placeholder="Search organizations by name, type, services..." class="search-input">
                        <button class="search-button" id="search-organizations-btn">🔍</button>
                    </div>
                </div>

                <div class="organizations-list-container">
                    <div class="organizations-count">
                        <span id="organizations-count">${organizations.length}</span> organizations found
                    </div>
                    <div class="organizations-list" id="organizations-list">
                        ${this.renderOrganizationsList(organizations)}
                    </div>
                </div>
            </div>
        `;
    }

    async loadBikesManagementContent() {
        const bikes = await this.bikeManager.getAllBikes();

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>BIKE MANAGEMENT</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="add-bike">
                            <span class="button-icon">🚲</span>
                            Register Bike
                        </button>
                        <button class="secondary-button" data-action="back-to-records">
                            <span class="button-icon">←</span>
                            Back to Records
                        </button>
                    </div>
                </div>

                <div class="search-section">
                    <div class="search-bar">
                        <input type="text" id="bikes-search" placeholder="Search bikes by serial, make, model, owner..." class="search-input">
                        <button class="search-button" id="search-bikes-btn">🔍</button>
                    </div>
                    <div class="filter-section">
                        <select id="bike-status-filter" class="filter-select">
                            <option value="">All Bikes</option>
                            <option value="registered">Registered</option>
                            <option value="stolen">Stolen</option>
                            <option value="recovered">Recovered</option>
                        </select>
                    </div>
                </div>

                <div class="bikes-list-container">
                    <div class="bikes-count">
                        <span id="bikes-count">${bikes.length}</span> bikes found
                    </div>
                    <div class="bikes-list" id="bikes-list">
                        ${this.renderBikesList(bikes)}
                    </div>
                </div>
            </div>
        `;
    }

    renderPeopleList(people) {
        console.log(`🎨 renderPeopleList called with ${people.length} people:`, people);

        if (people.length === 0) {
            console.log('📝 Rendering no-records message');
            return '<div class="no-records">No people records found. Click "Add Person" to create the first record.</div>';
        }

        console.log('📝 Rendering people cards...');
        const renderedCards = people.map(person => `
            <div class="person-card" data-person-id="${person.id}">
                <div class="person-avatar">
                    <div class="avatar-placeholder">
                        ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                    </div>
                </div>
                <div class="person-info">
                    <div class="person-name">
                        ${person.first_name || ''} ${person.last_name || ''}
                    </div>
                    <div class="person-details">
                        ${person.email ? `<span class="detail-item">📧 ${person.email}</span>` : ''}
                        ${person.phone ? `<span class="detail-item">📞 ${person.phone}</span>` : ''}
                        ${person.housing_status ? `<span class="detail-item">🏠 ${person.housing_status}</span>` : ''}
                    </div>
                    <div class="person-meta">
                        ID: ${person.id} | Created: ${this.uiUtilities.formatDate(person.created_at)}
                    </div>
                </div>
                <div class="person-actions">
                    <button class="action-button view-button" data-action="view-person" data-person-id="${person.id}">
                        View Details
                    </button>
                </div>
            </div>
        `);

        const finalHTML = renderedCards.join('');
        console.log(`📝 Final rendered HTML length: ${finalHTML.length} characters`);
        console.log('📝 First 200 characters of rendered HTML:', finalHTML.substring(0, 200));

        return finalHTML;
    }

    renderBikesList(bikes) {
        if (bikes.length === 0) {
            return '<div class="no-records">No bike records found. Click "Register Bike" to create the first record.</div>';
        }

        return bikes.map(bike => `
            <div class="bike-card ${bike.is_stolen ? 'stolen' : ''}" data-bike-id="${bike.id}">
                <div class="bike-status">
                    ${bike.is_stolen ?
                        '<span class="status-badge stolen">STOLEN</span>' :
                        '<span class="status-badge registered">REGISTERED</span>'
                    }
                </div>
                <div class="bike-info">
                    <div class="bike-header">
                        <div class="bike-make-model">
                            ${bike.make} ${bike.model}
                        </div>
                        <div class="bike-serial">
                            Serial: ${bike.serial_number}
                        </div>
                    </div>
                    <div class="bike-details">
                        <span class="detail-item">🎨 ${bike.color}</span>
                        <span class="detail-item">👤 ${bike.owner_name}</span>
                        ${bike.owner_email ? `<span class="detail-item">📧 ${bike.owner_email}</span>` : ''}
                        ${bike.value ? `<span class="detail-item">💰 $${bike.value}</span>` : ''}
                    </div>
                    <div class="bike-meta">
                        Registered: ${this.uiUtilities.formatDate(bike.registered_at)}
                        ${bike.is_stolen && bike.theft_date ? ` | Stolen: ${this.uiUtilities.formatDate(bike.theft_date)}` : ''}
                    </div>
                </div>
                <div class="bike-actions">
                    <button class="action-button view-button" data-action="view-bike" data-bike-id="${bike.id}">
                        View Details
                    </button>
                    ${bike.is_stolen ?
                        `<button class="action-button recovery-button" data-action="mark-bike-recovered" data-bike-id="${bike.id}">
                            Mark Recovered
                        </button>` :
                        `<button class="action-button report-button" data-action="report-bike-stolen" data-bike-id="${bike.id}">
                            Report Stolen
                        </button>`
                    }
                </div>
            </div>
        `).join('');
    }

    renderOrganizationsList(organizations) {
        if (organizations.length === 0) {
            return '<div class="no-records">No organization records found. Click "Add Organization" to create the first record.</div>';
        }

        return organizations.map(org => `
            <div class="organization-card" data-organization-id="${org.id}">
                <div class="organization-icon">
                    <div class="org-icon-placeholder">
                        ${(org.name?.[0] || '?').toUpperCase()}
                    </div>
                </div>
                <div class="organization-info">
                    <div class="organization-name">
                        ${org.name || 'Unnamed Organization'}
                    </div>
                    <div class="organization-details">
                        ${org.organization_type ? `<span class="detail-item">🏷️ ${org.organization_type}</span>` : ''}
                        ${org.phone ? `<span class="detail-item">📞 ${org.phone}</span>` : ''}
                        ${org.email ? `<span class="detail-item">📧 ${org.email}</span>` : ''}
                        ${org.partnership_type ? `<span class="detail-item">🤝 ${org.partnership_type}</span>` : ''}
                    </div>
                    <div class="organization-services">
                        ${org.services_provided ? `Services: ${org.services_provided.substring(0, 100)}${org.services_provided.length > 100 ? '...' : ''}` : 'No services listed'}
                    </div>
                    ${org.services_tags && org.services_tags.length > 0 ? `
                        <div class="services-tags">
                            ${org.services_tags.map(tag => `<span class="service-tag">${tag}</span>`).join('')}
                        </div>
                    ` : ''}
                    <div class="organization-meta">
                        ID: ${org.id} | Status: ${org.status || 'Active'} | Created: ${this.uiUtilities.formatDate(org.created_at)}
                    </div>
                </div>
                <div class="organization-actions">
                    <button class="action-button view-button" data-action="view-organization" data-organization-id="${org.id}">
                        View Details
                    </button>
                    <button class="action-button edit-button" data-action="edit-organization" data-organization-id="${org.id}">
                        Edit
                    </button>
                </div>
            </div>
        `).join('');
    }

    async loadPersonDetailContent(personId) {
        const person = await this.data.get('people', personId);
        if (!person) {
            return '<div class="error">Person not found</div>';
        }

        this.selectedPerson = person;

        return `
            <div class="content-section">
                <div class="section-header">
                    <h2>PERSON DETAILS</h2>
                    <div class="header-actions">
                        <button class="primary-button" data-action="edit-person" data-person-id="${person.id}">
                            <span class="button-icon">✏️</span>
                            Edit Person
                        </button>
                        <button class="secondary-button" data-action="back-to-people">
                            <span class="button-icon">←</span>
                            Back to People
                        </button>
                    </div>
                </div>

                <div class="person-detail-container">
                    <div class="person-detail-header">
                        <div class="person-avatar-large">
                            <div class="avatar-placeholder-large">
                                ${(person.first_name?.[0] || '?').toUpperCase()}${(person.last_name?.[0] || '').toUpperCase()}
                            </div>
                        </div>
                        <div class="person-title-info">
                            <h3 class="person-full-name">
                                ${person.first_name || ''} ${person.last_name || ''}
                            </h3>
                            <div class="person-id">ID: ${person.id}</div>
                            <div class="person-created">Created: ${this.uiUtilities.formatDate(person.created_at)}</div>
                        </div>
                    </div>

                    <div class="person-detail-tabs">
                        <button class="person-detail-tab active" data-tab="personal">Personal Info</button>
                        <button class="person-detail-tab" data-tab="aliases">Aliases</button>
                        <button class="person-detail-tab" data-tab="pets">Pets</button>
                        <button class="person-detail-tab" data-tab="medical">Medical</button>
                        <button class="person-detail-tab" data-tab="disabilities">Disabilities</button>
                        <button class="person-detail-tab" data-tab="case-management">Case Management</button>
                        <button class="person-detail-tab" data-tab="barriers">Service Barriers</button>
                        <button class="person-detail-tab" data-tab="support">Support Network</button>
                        <button class="person-detail-tab" data-tab="criminal-justice">Criminal Justice</button>
                        <button class="person-detail-tab" data-tab="activities">Activities</button>
                    </div>

                    <div class="person-detail-content">
                        <div class="tab-content active" id="personal-tab">
                            ${this.generatePersonalInfoTab(person)}
                        </div>
                        <div class="tab-content" id="aliases-tab">
                            ${this.generateAliasesTab(person)}
                        </div>
                        <div class="tab-content" id="pets-tab">
                            ${this.generatePetsTab(person)}
                        </div>
                        <div class="tab-content" id="medical-tab">
                            ${this.generateMedicalTab(person)}
                        </div>
                        <div class="tab-content" id="disabilities-tab">
                            ${this.generateDisabilitiesTab(person)}
                        </div>
                        <div class="tab-content" id="case-management-tab">
                            ${this.generateCaseManagementTab(person)}
                        </div>
                        <div class="tab-content" id="barriers-tab">
                            ${this.generateServiceBarriersTab(person)}
                        </div>
                        <div class="tab-content" id="support-tab">
                            ${this.generateSupportNetworkTab(person)}
                        </div>
                        <div class="tab-content" id="criminal-justice-tab">
                            <div id="justice-tab-content">
                                <!-- Justice module will be mounted here -->
                            </div>
                        </div>
                        <div class="tab-content" id="activities-tab">
                            ${this.generateActivitiesTab(person)}
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePersonalInfoTab(person) {
        return `
            <div class="detail-sections">
                <div class="detail-section">
                    <h4>Personal Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>First Name:</label>
                            <span>${person.first_name || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Name:</label>
                            <span>${person.last_name || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Date of Birth:</label>
                            <span>${person.date_of_birth || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Gender:</label>
                            <span>${person.gender || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Contact Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Email:</label>
                            <span>${person.email || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Phone:</label>
                            <span>${person.phone || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Emergency Contact:</label>
                            <span>${person.emergency_contact || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Emergency Phone:</label>
                            <span>${person.emergency_phone || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Housing & Services</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Housing Status:</label>
                            <span>${person.housing_status || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Services Received:</label>
                            <span>${person.services_received || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Special Needs:</label>
                            <span>${person.special_needs || 'Not provided'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Medical Conditions:</label>
                            <span>${person.medical_conditions || 'Not provided'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Additional Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item full-width">
                            <label>Notes:</label>
                            <span>${person.notes || 'No additional notes'}</span>
                        </div>
                    </div>
                </div>

                <div class="detail-section">
                    <h4>Record Information</h4>
                    <div class="detail-grid">
                        <div class="detail-item">
                            <label>Created By:</label>
                            <span>${person.created_by || 'System'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Created At:</label>
                            <span>${this.uiUtilities.formatDateTime(person.created_at)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Last Updated:</label>
                            <span>${this.uiUtilities.formatDateTime(person.updated_at) || 'Never'}</span>
                        </div>
                        <div class="detail-item">
                            <label>Updated By:</label>
                            <span>${person.updated_by || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generatePetsTab(person) {
        return `
            <div class="pets-section">
                <div class="section-header">
                    <h4>Pet Information</h4>
                    <button class="primary-button" data-action="add-pet" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Pet
                    </button>
                </div>
                <div id="pets-list" class="pets-list">
                    <div class="loading">Loading pets...</div>
                </div>
            </div>
        `;
    }

    generateAliasesTab(person) {
        return `
            <div class="aliases-section">
                <div class="section-header">
                    <h4>Known Aliases & Alternate Names</h4>
                    <button class="primary-button" data-action="add-alias" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Alias
                    </button>
                </div>
                <div id="aliases-list" class="aliases-list">
                    <div class="loading">Loading aliases...</div>
                </div>
            </div>
        `;
    }

    generateMedicalTab(person) {
        return `
            <div class="medical-section">
                <div class="section-header">
                    <h4>📋 Medical Information & Health Tracking</h4>
                    <div class="medical-actions">
                        <button class="primary-button" data-action="add-medical-issue" data-person-id="${person.id}">
                            <span class="button-icon">+</span>
                            Add Medical Issue
                        </button>
                        <button class="secondary-button" data-action="add-infection-tracking" data-person-id="${person.id}">
                            <span class="button-icon">🦠</span>
                            Track Infection
                        </button>
                        <button class="secondary-button" data-action="add-mental-health-note" data-person-id="${person.id}">
                            <span class="button-icon">🧠</span>
                            Mental Health
                        </button>
                        <button class="secondary-button" data-action="add-addiction-note" data-person-id="${person.id}">
                            <span class="button-icon">💊</span>
                            Addiction Note
                        </button>
                    </div>
                </div>
                
                <div class="medical-overview">
                    <div class="medical-categories">
                        <div class="category-filter active" data-filter="all">All Issues</div>
                        <div class="category-filter" data-filter="Physical Health">Physical</div>
                        <div class="category-filter" data-filter="Mental Health">Mental Health</div>
                        <div class="category-filter" data-filter="Addiction">Addiction</div>
                        <div class="category-filter" data-filter="Infection">Infections</div>
                        <div class="category-filter" data-filter="active">Active Only</div>
                        <div class="category-filter" data-filter="critical">Critical</div>
                    </div>
                </div>

                <div id="medical-issues-list" class="medical-issues-list">
                    <div class="loading">Loading medical issues...</div>
                </div>

                <div class="medical-summary">
                    <div class="summary-section">
                        <h5>🚨 Critical Alerts</h5>
                        <div id="critical-medical-alerts" class="critical-alerts">
                            <!-- Critical medical issues will be displayed here -->
                        </div>
                    </div>
                    <div class="summary-section">
                        <h5>📊 Health Summary</h5>
                        <div id="health-summary-stats" class="health-stats">
                            <!-- Health statistics will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    generateActivitiesTab(person) {
        return `
            <div class="activity-section">
                <div class="section-header">
                    <h4>Recent Activities</h4>
                    <button class="primary-button" data-action="add-person-activity" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Activity
                    </button>
                </div>
                <div id="person-activities" class="activities-list">
                    <div class="loading">Loading activities...</div>
                </div>
            </div>
        `;
    }

    generateDisabilitiesTab(person) {
        return `
            <div class="disabilities-section">
                <div class="section-header">
                    <h4>Disability Information</h4>
                    <button class="primary-button" data-action="add-disability" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Disability
                    </button>
                </div>
                <div id="disabilities-list" class="linked-records-list">
                    <div class="loading">Loading disabilities...</div>
                </div>
            </div>
        `;
    }

    generateCaseManagementTab(person) {
        return `
            <div class="case-management-section">
                <div class="section-header">
                    <h4>Case Management</h4>
                    <button class="primary-button" data-action="add-case-management" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Case Management
                    </button>
                </div>
                <div id="case-management-list" class="linked-records-list">
                    <div class="loading">Loading case management...</div>
                </div>
            </div>
        `;
    }

    generateServiceBarriersTab(person) {
        return `
            <div class="service-barriers-section">
                <div class="section-header">
                    <h4>Service Barriers</h4>
                    <button class="primary-button" data-action="add-service-barrier" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Service Barrier
                    </button>
                </div>
                <div id="service-barriers-list" class="linked-records-list">
                    <div class="loading">Loading service barriers...</div>
                </div>
            </div>
        `;
    }

    generateSupportNetworkTab(person) {
        return `
            <div class="support-network-section">
                <div class="section-header">
                    <h4>Support Network</h4>
                    <button class="primary-button" data-action="add-support-contact" data-person-id="${person.id}">
                        <span class="button-icon">+</span>
                        Add Support Contact
                    </button>
                </div>
                <div id="support-network-list" class="linked-records-list">
                    <div class="loading">Loading support network...</div>
                </div>
            </div>
        `;
    }

    // generateCriminalJusticeTab method removed - replaced by Justice module


    async loadReportsContent() {
        return reportTemplates.reportMainView();
    }

    async loadSystemContent() {
        return await this.systemSettingsManager.loadSystemContent();
    }

    async loadPropertyContent() {
        // Load property management main view
        const properties = await this.data.search('property_records', {});
        return `
            <div class="property-management-container">
                <div class="property-header">
                    <h2>Property Management</h2>
                    <div class="property-actions">
                        <button class="primary-button" onclick="app.commands.execute('list-found-property')">Found Property</button>
                        <button class="primary-button" onclick="app.commands.execute('list-missing-property')">Missing Property</button>
                        <button class="primary-button" onclick="app.commands.execute('log-property-recovery')">Log Recovery</button>
                    </div>
                </div>
                <div class="property-content">
                    <div class="property-stats">
                        <div class="stat-card">
                            <h3>Total Properties</h3>
                            <div class="stat-number">${properties.length}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Found Items</h3>
                            <div class="stat-number">${properties.filter(p => p.status === 'found').length}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Returned Items</h3>
                            <div class="stat-number">${properties.filter(p => p.status === 'returned').length}</div>
                        </div>
                    </div>
                    <div class="property-list">
                        <h3>Recent Property Records</h3>
                        ${properties.slice(0, 10).map(property => `
                            <div class="property-item">
                                <div class="property-info">
                                    <strong>${property.property_type || 'Unknown'}</strong>
                                    <span>${property.description || 'No description'}</span>
                                    <small>Found: ${property.found_date || 'Unknown date'}</small>
                                </div>
                                <div class="property-status">${property.status || 'Unknown'}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    async loadEncampmentsContent() {
        // Load encampments management main view
        const encampments = await this.data.search('encampments', {});
        return `
            <div class="encampments-management-container">
                <div class="encampments-header">
                    <h2>Encampments Management</h2>
                    <div class="encampments-actions">
                        <button class="primary-button" onclick="app.commands.execute('add-encampment')">Add Encampment</button>
                    </div>
                </div>
                <div class="encampments-content">
                    <div class="encampments-stats">
                        <div class="stat-card">
                            <h3>Total Encampments</h3>
                            <div class="stat-number">${encampments.length}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Active</h3>
                            <div class="stat-number">${encampments.filter(e => e.status === 'active').length}</div>
                        </div>
                        <div class="stat-card">
                            <h3>Inactive</h3>
                            <div class="stat-number">${encampments.filter(e => e.status === 'inactive').length}</div>
                        </div>
                    </div>
                    <div class="encampments-list">
                        <h3>Encampments List</h3>
                        ${encampments.map(encampment => `
                            <div class="encampment-item" onclick="app.viewEncampmentDetail('${encampment.id}')">
                                <div class="encampment-info">
                                    <strong>${encampment.name || 'Unnamed Encampment'}</strong>
                                    <span>${encampment.location || 'No location'}</span>
                                    <small>Population: ${encampment.estimated_population || 'Unknown'}</small>
                                </div>
                                <div class="encampment-status">${encampment.status || 'Unknown'}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    async initializeAdmin() {
        try {
            console.log('🔧 Initializing admin interface...');
            await this.adminManager.initialize();
            console.log('✅ Admin interface initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize admin interface:', error);
            this.ui.showDialog('Error', `Failed to initialize admin interface: ${error.message}`, 'error');
        }
    }

    goToDispatch(incidentId = null) {
        // Navigate to the incidents tab (which shows dispatch view)
        const incidentsTab = document.querySelector('[data-tab="incidents"]');
        if (incidentsTab) {
            incidentsTab.click();

            // If an incident ID is provided, select it after a short delay
            if (incidentId) {
                setTimeout(() => {
                    this.selectIncident(incidentId);
                }, 100);
            }
        } else {
            console.error('Incidents tab not found');
        }
    }

    selectIncident(incidentId) {
        // This method should select an incident in the incidents interface
        if (this.incidentManagement && typeof this.incidentManagement.selectIncident === 'function') {
            this.incidentManagement.selectIncident(incidentId);
        } else {
            console.warn('Incident management not available or selectIncident method not found');
        }
    }

    async loadFoundPropertyListContent() {
        const foundProperties = await this.data.search('property_records', { status: 'found' });
        return `
            <div class="property-list-container">
                <div class="property-list-header">
                    <h2>Found Property</h2>
                    <button class="primary-button" onclick="app.commands.execute('back-to-property')">Back to Property</button>
                </div>
                <div class="property-list">
                    ${foundProperties.map(property => `
                        <div class="property-item">
                            <div class="property-info">
                                <strong>${property.property_type || 'Unknown'}</strong>
                                <span>${property.description || 'No description'}</span>
                                <small>Found: ${property.found_date || 'Unknown date'} at ${property.found_location || 'Unknown location'}</small>
                            </div>
                            <div class="property-actions">
                                <button class="action-btn" onclick="app.viewPropertyDetail('${property.id}')">View</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    async loadMissingPropertyListContent() {
        const missingProperties = await this.data.search('property_records', { status: 'missing' });
        return `
            <div class="property-list-container">
                <div class="property-list-header">
                    <h2>Missing Property</h2>
                    <button class="primary-button" onclick="app.commands.execute('back-to-property')">Back to Property</button>
                </div>
                <div class="property-list">
                    ${missingProperties.map(property => `
                        <div class="property-item">
                            <div class="property-info">
                                <strong>${property.property_type || 'Unknown'}</strong>
                                <span>${property.description || 'No description'}</span>
                                <small>Reported: ${property.created_at || 'Unknown date'}</small>
                            </div>
                            <div class="property-actions">
                                <button class="action-btn" onclick="app.viewPropertyDetail('${property.id}')">View</button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }

    async loadAddEncampmentContent() {
        return `
            <div class="encampment-form-container">
                <div class="encampment-form-header">
                    <h2>Add New Encampment</h2>
                    <button class="secondary-button" onclick="app.loadTabContent('encampments')">Back to Encampments</button>
                </div>
                <form id="add-encampment-form" class="encampment-form">
                    <div class="form-group">
                        <label for="encampment-name">Name:</label>
                        <input type="text" id="encampment-name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="encampment-location">Location:</label>
                        <input type="text" id="encampment-location" name="location" required>
                    </div>
                    <div class="form-group">
                        <label for="encampment-population">Estimated Population:</label>
                        <input type="number" id="encampment-population" name="estimated_population">
                    </div>
                    <div class="form-group">
                        <label for="encampment-status">Status:</label>
                        <select id="encampment-status" name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="cleared">Cleared</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="encampment-description">Description:</label>
                        <textarea id="encampment-description" name="description" rows="4"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="primary-button">Save Encampment</button>
                        <button type="button" class="secondary-button" onclick="app.loadTabContent('encampments')">Cancel</button>
                    </div>
                </form>
            </div>
        `;
    }

    async loadEncampmentDetailContent(encampmentId) {
        const encampment = await this.data.get('encampments', encampmentId);
        if (!encampment) {
            return '<div class="error">Encampment not found</div>';
        }

        return `
            <div class="encampment-detail-container">
                <div class="encampment-detail-header">
                    <h2>${encampment.name || 'Unnamed Encampment'}</h2>
                    <button class="secondary-button" onclick="app.loadTabContent('encampments')">Back to Encampments</button>
                </div>
                <div class="encampment-detail-content">
                    <div class="detail-section">
                        <h3>Basic Information</h3>
                        <p><strong>Location:</strong> ${encampment.location || 'Not specified'}</p>
                        <p><strong>Status:</strong> ${encampment.status || 'Unknown'}</p>
                        <p><strong>Estimated Population:</strong> ${encampment.estimated_population || 'Unknown'}</p>
                        <p><strong>Description:</strong> ${encampment.description || 'No description'}</p>
                    </div>
                    <div class="detail-section">
                        <h3>Safety & Services</h3>
                        <p><strong>Safety Concerns:</strong> ${encampment.safety_concerns || 'None noted'}</p>
                        <p><strong>Services Needed:</strong> ${encampment.services_needed || 'None specified'}</p>
                        <p><strong>Last Visited:</strong> ${encampment.last_visited || 'Never'}</p>
                    </div>
                    <div class="detail-actions">
                        <button class="primary-button" onclick="app.editEncampment('${encampment.id}')">Edit</button>
                        <button class="secondary-button" onclick="app.addEncampmentVisit('${encampment.id}')">Add Visit</button>
                    </div>
                </div>
            </div>
        `;
    }

    async loadAdminContent() {
        return `
            <div class="admin-container">
                <div class="admin-header">
                    <h2>ADMIN PANEL</h2>
                    <div class="admin-user-info">
                        Logged in as: ${this.currentUser?.email} (Administrator)
                    </div>
                </div>

                <div class="admin-sections">
                    <div class="admin-nav">
                        <button class="admin-nav-btn active" data-section="items">ITEM MANAGEMENT</button>
                        <button class="admin-nav-btn" data-section="users">USER MANAGEMENT</button>
                        <button class="admin-nav-btn" data-section="roles">ROLE MANAGEMENT</button>
                        <button class="admin-nav-btn" data-section="ai-settings">AI SETTINGS</button>
                    </div>

                    <div class="admin-section" id="items-section">
                        <div class="section-header">
                            <h3>ITEM MANAGEMENT</h3>
                            <button class="primary-button" id="add-item-btn">ADD NEW ITEM</button>
                        </div>

                        <div class="item-controls">
                            <div class="search-controls">
                                <input type="text" id="item-search" placeholder="Search items..." class="search-input">
                                <select id="category-filter" class="filter-select">
                                    <option value="">All Categories</option>
                                    <option value="food">Food</option>
                                    <option value="hygiene">Hygiene</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="medical">Medical</option>
                                    <option value="other">Other</option>
                                </select>
                                <select id="status-filter" class="filter-select">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div class="items-list" id="items-list">
                            <div class="loading">Loading items...</div>
                        </div>
                    </div>

                    <div class="admin-section" id="users-section" style="display: none;">
                        <div class="section-header">
                            <h3>USER MANAGEMENT</h3>
                            <button class="primary-button" id="add-user-btn">ADD NEW USER</button>
                        </div>

                        <div class="user-controls">
                            <div class="search-controls">
                                <input type="text" id="user-search" placeholder="Search users..." class="search-input">
                                <select id="role-filter" class="filter-select">
                                    <option value="">All Roles</option>
                                    <option value="iharc_staff">Staff</option>
                                    <option value="iharc_admin">Admin</option>
                                </select>
                            </div>
                        </div>

                        <div class="users-list" id="users-list">
                            <div class="loading">Loading users...</div>
                        </div>
                    </div>

                    <div class="admin-section" id="roles-section" style="display: none;">
                        <div class="section-header">
                            <h3>ROLE MANAGEMENT</h3>
                            <button class="primary-button" id="add-role-btn">CREATE CUSTOM ROLE</button>
                        </div>

                        <div class="role-controls">
                            <div class="search-controls">
                                <input type="text" id="role-search" placeholder="Search roles..." class="search-input">
                                <select id="role-type-filter" class="filter-select">
                                    <option value="">All Roles</option>
                                    <option value="system">System Roles</option>
                                    <option value="custom">Custom Roles</option>
                                </select>
                            </div>
                        </div>

                        <div class="roles-list" id="roles-list">
                            <div class="loading">Loading roles...</div>
                        </div>
                    </div>

                    <div class="admin-section" id="ai-settings-section" style="display: none;">
                        <div class="section-header">
                            <h3>AI TEXT ENHANCEMENT SETTINGS</h3>
                            <button class="primary-button" id="save-ai-settings-btn">SAVE SETTINGS</button>
                        </div>

                        <div class="ai-settings-form">
                            <div class="form-group">
                                <label for="gemini-model-select">Google Gemini Model:</label>
                                <div class="model-select-container">
                                    <select id="gemini-model-select" class="form-control">
                                        <option value="">Loading models...</option>
                                    </select>
                                    <button type="button" class="secondary-button refresh-models-btn" id="refresh-models-btn">
                                        🔄 Refresh Models
                                    </button>
                                </div>
                                <small class="form-help">
                                    Select the Google Gemini model to use for AI text enhancement. 
                                    Flash models are faster, Pro models are more capable. Click refresh to get latest models.
                                </small>
                            </div>

                            <div class="form-group">
                                <label for="ai-system-prompt">System Prompt:</label>
                                <textarea id="ai-system-prompt" class="form-control ai-prompt-textarea" rows="8" 
                                          placeholder="Enter the system prompt that will guide AI text enhancement..."></textarea>
                                <small class="form-help">
                                    This prompt instructs the AI on how to enhance text. Be specific about tone, 
                                    style, and requirements for your field reports and documentation.
                                </small>
                            </div>

                            <div class="ai-settings-status">
                                <div class="status-item">
                                    <span class="status-label">API Status:</span>
                                    <span id="ai-api-status" class="status-value">Checking...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Models Cache:</span>
                                    <span id="ai-cache-status" class="status-value">Not loaded</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">Last Updated:</span>
                                    <span id="ai-last-updated" class="status-value">Never</span>
                                </div>
                            </div>

                            <div class="ai-test-section">
                                <h4>Test AI Enhancement</h4>
                                <div class="form-group">
                                    <textarea id="ai-test-input" class="form-control" rows="3" 
                                              placeholder="Enter sample text to test AI enhancement..."></textarea>
                                </div>
                                <button class="secondary-button" id="test-ai-enhancement-btn">TEST ENHANCEMENT</button>
                                <div id="ai-test-result" class="ai-test-result" style="display: none;">
                                    <h5>Enhanced Text:</h5>
                                    <div class="enhanced-text-preview"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async loadDispatchContent() {
        return `
            <div class="dispatch-container">
                <div class="dispatch-grid">
                    <!-- Left Pane - Incident List -->
                    <div class="dispatch-incident-list">
                        <div class="dispatch-header">
                            <h3>🚨 ACTIVE INCIDENTS</h3>
                            <div class="dispatch-controls">
                                <span class="incident-count" id="dispatch-incident-count">0</span>
                                <button class="refresh-btn" id="dispatch-refresh">↻</button>
                            </div>
                        </div>
                        <div class="incident-list-container" id="dispatch-incident-list">
                            <div class="loading">Loading incidents...</div>
                        </div>
                    </div>

                    <!-- Right Pane - Incident Detail -->
                    <div class="dispatch-incident-detail">
                        <div class="dispatch-header">
                            <h3>📋 INCIDENT DETAILS</h3>
                            <div class="dispatch-shortcuts">
                                <span class="shortcut-hint">F2: Assign | F3: Status | F4: Note | F8: Close</span>
                            </div>
                        </div>
                        <div class="incident-detail-container" id="dispatch-incident-detail">
                            <div class="no-selection">Select an incident to view details</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }



    setupMenuHandlers() {
        // Add click handlers to menu items and buttons
        document.addEventListener('click', async (e) => {
            const menuItem = e.target.closest('.menu-item');
            const button = e.target.closest('button[data-action]');
            const personCard = e.target.closest('.person-card');
            const viewButton = e.target.closest('.view-button');
            const encampmentCard = e.target.closest('.encampment-item');
            const encampmentButton = e.target.closest('.encampment-item .action-button');

            // Handle menu items
            if (menuItem) {
                const action = menuItem.dataset.action;
                if (action) {
                    await this.handleMenuAction(action);
                }
            }

            // Handle buttons with data-action
            if (button) {
                const action = button.dataset.action;
                if (action) {
                    await this.handleMenuAction(action, button);
                }
            }

            // Handle person card clicks
            if (personCard && !viewButton) {
                const personId = personCard.dataset.personId;
                if (personId) {
                    await this.commands.executeCommand('view-person-detail', { personId: personId });
                }
            }

            // Handle view button clicks
            if (viewButton) {
                e.stopPropagation();
                const personId = viewButton.dataset.personId;
                if (personId) {
                    await this.commands.executeCommand('view-person-detail', { personId: personId });
                }
            }

            // Handle encampment card clicks
            if (encampmentCard && !encampmentButton) {
                const encampmentId = encampmentCard.dataset.encampmentId;
                if (encampmentId) {
                    await this.viewEncampmentDetail(encampmentId);
                }
            }

            // Handle encampment button clicks (prevent card click)
            if (encampmentButton) {
                e.stopPropagation();
            }
        });
    }

    setupDataChangeListeners() {
        // Listen for real-time data changes
        // Removed conflicting dataChange listener that was causing full view refresh
        // The incident-specific dataChange handler in initializeUnifiedIncidents() handles incident updates properly
    }

    handleDataChange(table, operation, record) {
        console.log(`🔄 Data change detected: ${table} ${operation}`, record?.id);

        // Specific table refresh logic
        if (table === 'people') {
            console.log(`👥 People data changed, refreshing people list...`);
            this.refreshPeopleList();
        }

        // Refresh the current tab if it displays data from the changed table
        if (this.shouldRefreshCurrentView(table, operation)) {
            console.log(`🔄 Refreshing current view due to ${operation} in ${table}`);
            this.refreshCurrentView();
        }

        // Show notification for significant changes
        if (operation === 'insert' && table === 'incidents') {
            this.uiUtilities.showNotification(`New incident created: ${record.title || record.id}`, 'info');
        }
    }

    shouldRefreshCurrentView(table, operation) {
        const currentTab = this.currentTab;

        // Don't refresh view for 'refresh' operations - these are just cache updates
        // Only refresh for actual data changes (insert, update, delete)
        if (operation === 'refresh') {
            console.log(`🔄 Ignoring refresh operation for ${table} - cache update only`);
            return false;
        }

        // Map tabs to tables they display
        const tabTableMap = {
            'people': ['people', 'addresses'],
            'incidents': ['incidents'],
            'dispatch': ['incidents'],
            'property': ['property_records', 'addresses'],
            'bikes': ['bikes'],
            'encampments': ['encampments'],
            'admin': ['people', 'incidents', 'addresses', 'bikes', 'items', 'encampments']
        };

        const relevantTables = tabTableMap[currentTab] || [];
        return relevantTables.includes(table);
    }

    refreshCurrentView() {
        // Refresh the current tab content
        if (this.currentTab) {
            this.loadTabContent(this.currentTab);
        }
    }


    async handleMenuAction(action, element = null) {
        this.ui.setStatus(`Executing ${action}...`);

        try {
            // Handle navigation actions
            switch (action) {
                case 'manage-people':
                    await this.loadTabContent('records', 'people-management');
                    break;
                case 'manage-addresses':
                    await this.loadTabContent('records', 'addresses-management');
                    break;
                case 'manage-organizations':
                    await this.loadTabContent('records', 'organizations-management');
                    break;
                case 'manage-bikes':
                    await this.loadTabContent('records', 'bikes-management');
                    break;
                case 'back-to-records':
                    await this.loadTabContent('records');
                    break;
                case 'back-to-people':
                    await this.loadTabContent('records', 'people-management');
                    break;
                case 'back-to-address-list':
                    await this.loadTabContent('records', 'addresses-management');
                    break;
                case 'add-organization':
                    await this.addOrganization();
                    break;
                case 'view-person':
                case 'view-person-detail':
                    const personId = element?.dataset.personId;
                    if (personId) {
                        // Use the command system which properly delegates to the people management module
                        await this.commands.executeCommand('view-person-detail', { personId: personId });
                    }
                    break;
                case 'edit-person':
                    const editPersonId = element?.dataset.personId;
                    if (editPersonId) {
                        // Use the command system which properly delegates to the people management module
                        await this.commands.executeCommand('edit-person', { personId: editPersonId });
                    }
                    break;
                case 'add-pet':
                    const personIdForPet = element?.dataset.personId;
                    if (personIdForPet) {
                        await this.addPet(personIdForPet);
                    }
                    break;
                case 'edit-pet':
                    const petId = element?.dataset.petId;
                    if (petId) {
                        await this.editPet(petId);
                    }
                    break;
                case 'delete-pet':
                    const deletePetId = element?.dataset.petId;
                    if (deletePetId) {
                        await this.deletePet(deletePetId);
                    }
                    break;
                case 'add-medical-issue':
                    const personIdForMedical = element?.dataset.personId;
                    if (personIdForMedical) {
                        await this.addMedicalIssue(personIdForMedical);
                    }
                    break;
                case 'edit-medical-issue':
                    const medicalIssueId = element?.dataset.medicalIssueId;
                    if (medicalIssueId) {
                        await this.editMedicalIssue(medicalIssueId);
                    }
                    break;
                case 'delete-medical-issue':
                    const deleteMedicalIssueId = element?.dataset.medicalIssueId;
                    if (deleteMedicalIssueId) {
                        await this.deleteMedicalIssue(deleteMedicalIssueId);
                    }
                    break;
                case 'add-infection-tracking':
                    const personIdForInfection = element?.dataset.personId;
                    if (personIdForInfection) {
                        await this.addInfectionTracking(personIdForInfection);
                    }
                    break;
                case 'add-mental-health-note':
                    const personIdForMentalHealth = element?.dataset.personId;
                    if (personIdForMentalHealth) {
                        await this.addMentalHealthNote(personIdForMentalHealth);
                    }
                    break;
                case 'add-addiction-note':
                    const personIdForAddiction = element?.dataset.personId;
                    if (personIdForAddiction) {
                        await this.addAddictionNote(personIdForAddiction);
                    }
                    break;
                case 'add-alias':
                    const personIdForAlias = element?.dataset.personId;
                    if (personIdForAlias) {
                        await this.addPersonAlias(personIdForAlias);
                    }
                    break;
                case 'edit-alias':
                    const aliasId = element?.dataset.aliasId;
                    const aliasName = element?.dataset.aliasName;
                    if (aliasId && aliasName) {
                        await this.editPersonAlias(aliasId, aliasName);
                    }
                    break;
                case 'delete-alias':
                    const deleteAliasId = element?.dataset.aliasId;
                    if (deleteAliasId) {
                        await this.deletePersonAlias(deleteAliasId);
                    }
                    break;
                case 'list-found-property':
                    await this.loadTabContent('property', 'found-property-list');
                    break;
                case 'list-missing-property':
                    await this.loadTabContent('property', 'missing-property-list');
                    break;
                case 'log-property-recovery':
                    await this.loadTabContent('property', 'log-property-recovery');
                    break;
                case 'create-missing-report':
                    await this.loadTabContent('property', 'create-missing-report');
                    break;
                case 'add-bike':
                    await this.addBike();
                    break;
                case 'view-bike':
                    const bikeId = element?.dataset.bikeId;
                    if (bikeId) {
                        await this.viewBikeDetails(bikeId);
                    }
                    break;
                case 'report-bike-stolen':
                    const reportBikeId = element?.dataset.bikeId;
                    if (reportBikeId) {
                        await this.reportBikeStolen(reportBikeId);
                    }
                    break;
                case 'add-encampment':
                    await this.loadTabContent('encampments', 'add-encampment');
                    break;
                case 'view-encampment':
                    const encampmentId = element?.dataset.encampmentId;
                    if (encampmentId) {
                        await this.viewEncampmentDetail(encampmentId);
                    }
                    break;
                case 'edit-encampment':
                    const editEncampmentId = element?.dataset.encampmentId;
                    if (editEncampmentId) {
                        await this.editEncampment(editEncampmentId);
                    }
                    break;
                case 'back-to-encampments':
                    await this.loadTabContent('encampments');
                    break;
                case 'view-encampment-map':
                    const mapEncampmentId = element?.dataset.encampmentId;
                    if (mapEncampmentId) {
                        await this.viewEncampmentOnMap(mapEncampmentId);
                    }
                    break;
                case 'view-encampments-map':
                    await this.viewAllEncampmentsMap();
                    break;
                case 'record-encampment-visit':
                    const visitEncampmentId = element?.dataset.encampmentId;
                    if (visitEncampmentId) {
                        await this.showEncampmentVisitForm(visitEncampmentId);
                    }
                    break;
                case 'refresh-visit-history':
                    const refreshEncampmentId = element?.dataset.encampmentId;
                    if (refreshEncampmentId) {
                        await this.refreshEncampmentVisitHistory(refreshEncampmentId);
                    }
                    break;
                case 'view-visit-details':
                    const visitId = element?.dataset.visitId;
                    if (visitId) {
                        await this.showVisitDetails(visitId);
                    }
                    break;
                case 'mark-bike-recovered':
                    const recoveredBikeId = element?.dataset.bikeId;
                    if (recoveredBikeId) {
                        await this.markBikeRecovered(recoveredBikeId);
                    }
                    break;
                case 'create-incident-screen':
                    await this.loadTabContent('incidents', 'create-comprehensive-incident');
                    break;
                case 'search-incidents':
                    await this.loadTabContent('records', 'incident-search');
                    break;
                case 'report-incident':
                    await this.loadTabContent('incidents', 'create-comprehensive-incident');
                    break;
                case 'generate-resource-services-report':
                    await this.showResourceServicesModal();
                    break;
                case 'view-incident':
                    const incidentId = element?.dataset.incidentId;
                    if (incidentId) {
                        await this.viewIncidentFromSearch(incidentId);
                    }
                    break;
                case 'edit':
                case 'delete':
                case 'toggle':
                    // Admin.js handles these actions with its own event listeners
                    // No action needed here to prevent routing to command manager
                    break;

                // Justice Module Actions
                case 'justice:new-episode':
                    const personIdForEpisode = element?.dataset.personId;
                    if (personIdForEpisode) {
                        await this.showStartEpisodeWizard(personIdForEpisode);
                    }
                    break;
                case 'justice:view-episode':
                    const episodeId = element?.dataset.episodeId;
                    if (episodeId) {
                        await this.showEpisodeDetail(episodeId);
                    }
                    break;
                case 'justice:add-bail-hearing':
                    const bailEpisodeId = element?.dataset.episodeId;
                    if (bailEpisodeId) {
                        await this.openAddEventModal('bail-hearing', bailEpisodeId);
                    }
                    break;
                case 'justice:add-transfer':
                    const transferEpisodeId = element?.dataset.episodeId;
                    if (transferEpisodeId) {
                        await this.openAddEventModal('transfer', transferEpisodeId);
                    }
                    break;
                case 'justice:add-release-conditions':
                    const releaseEpisodeId = element?.dataset.episodeId;
                    if (releaseEpisodeId) {
                        await this.openAddEventModal('release-conditions', releaseEpisodeId);
                    }
                    break;
                case 'justice:add-court-appearance':
                    const courtEpisodeId = element?.dataset.episodeId;
                    if (courtEpisodeId) {
                        await this.openAddEventModal('court-appearance', courtEpisodeId);
                    }
                    break;
                case 'justice:add-sentence':
                    const sentenceEpisodeId = element?.dataset.episodeId;
                    if (sentenceEpisodeId) {
                        await this.openAddEventModal('sentence', sentenceEpisodeId);
                    }
                    break;
                case 'justice:add-warrant':
                    const warrantEpisodeId = element?.dataset.episodeId;
                    if (warrantEpisodeId) {
                        await this.openAddEventModal('warrant', warrantEpisodeId);
                    }
                    break;
                case 'refresh-status':
                    const statusEpisodeId = element?.dataset.episodeId;
                    if (statusEpisodeId) {
                        const container = element.closest('#justice-status-ribbon');
                        if (container) {
                            await this.refreshJusticeStatus(container, statusEpisodeId);
                        }
                    }
                    break;
                case 'close-modal':
                    const modal = element.closest('.modal-overlay');
                    if (modal) {
                        this.modalManagement.closeModal(modal.id);
                    }
                    break;

                // Dashboard quick action buttons
                case 'new-incident':
                    await this.loadTabContent('incidents', 'create-comprehensive-incident');
                    break;
                case 'log-activity':
                    await this.activityManagement.showActivityTypeSelection();
                    break;
                case 'new-outreach':
                    await this.commands.executeCommand('outreach-transactions');
                    break;
                case 'view-alerts':
                    await this.commands.executeCommand('view-weather-alerts');
                    break;

                default:
                    // Handle other commands through command manager
                    // Extract arguments for commands based on data attributes
                    const args = {};
                    let firstArg = null;
                    
                    if (element) {
                        // For person-related add commands, use person ID
                        if (element.dataset.personId) {
                            args.personId = element.dataset.personId;
                        }
                        // For delete commands, use record ID as first argument
                        if (element.dataset.recordId) {
                            args.recordId = element.dataset.recordId;
                            // For delete commands, also pass as first argument for compatibility
                            if (action.startsWith('delete-')) {
                                firstArg = element.dataset.recordId;
                            }
                        }
                        // Handle additional ID attribute names for delete commands
                        if (element.dataset.aliasId && action === 'delete-alias') {
                            firstArg = element.dataset.aliasId;
                            args.aliasId = element.dataset.aliasId;
                        }
                        if (element.dataset.medicalIssueId && action === 'delete-medical-issue') {
                            firstArg = element.dataset.medicalIssueId;
                            args.medicalIssueId = element.dataset.medicalIssueId;
                        }
                        if (element.dataset.petId && action === 'delete-pet') {
                            firstArg = element.dataset.petId;
                            args.petId = element.dataset.petId;
                        }
                        // For organization-related commands, use organization ID
                        if (element.dataset.organizationId) {
                            args.organizationId = element.dataset.organizationId;
                        }
                        // For address-related commands, use address ID
                        if (element.dataset.addressId) {
                            args.addressId = element.dataset.addressId;
                        }
                        // For vehicle-related commands, use vehicle ID
                        if (element.dataset.vehicleId) {
                            args.vehicleId = element.dataset.vehicleId;
                        }
                        // For incident-related commands, use incident ID
                        if (element.dataset.incidentId) {
                            args.incidentId = element.dataset.incidentId;
                        }
                        // For outreach transaction commands, use transaction ID
                        if (element.dataset.transactionId) {
                            args.transactionId = element.dataset.transactionId;
                            // For delete outreach transaction, also pass as first argument
                            if (action === 'delete-outreach-transaction') {
                                firstArg = element.dataset.transactionId;
                            }
                        }
                        // For justice episode commands, use episode ID
                        if (element.dataset.episodeId) {
                            args.episodeId = element.dataset.episodeId;
                            // For justice commands, also pass as first argument
                            if (action.startsWith('je:')) {
                                firstArg = element.dataset.episodeId;
                            }
                        }
                    }
                    
                    // If we have a first argument for delete commands, pass it as array
                    const commandArgs = firstArg ? [firstArg, args] : args;
                    await this.commands.executeCommand(action, commandArgs);
            }

            this.ui.setStatus('Ready');
        } catch (error) {
            console.error('Command execution error:', error);
            this.ui.setStatus('Error');
        }
    }



    showPersonEditOptions(personRecord, firstName, lastName) {
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Person Record Updated</h3>
                </div>
                <div class="modal-body">
                    <p class="success-message">✅ Person record for <strong>${firstName} ${lastName}</strong> has been updated successfully!</p>
                    <p>Would you like to manage aliases or other information?</p>

                    <div class="edit-options">
                        <div class="menu-grid">
                            <div class="menu-item" data-action="manage-aliases">
                                <div class="menu-icon">🏷️</div>
                                <div class="menu-title">Manage Aliases</div>
                                <div class="menu-desc">Add, edit, or remove aliases and alternate names</div>
                            </div>
                            <div class="menu-item" data-action="view-details">
                                <div class="menu-icon">👁️</div>
                                <div class="menu-title">View Details</div>
                                <div class="menu-desc">Go to the full person detail page</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Done</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle option selection
        modal.addEventListener('click', (e) => {
            const menuItem = e.target.closest('.menu-item');
            if (menuItem) {
                const action = menuItem.dataset.action;
                modal.remove();
                this.handlePersonEditAction(action, personRecord);
            }
        });
    }

    async handlePersonEditAction(action, personRecord) {
        try {
            switch (action) {
                case 'manage-aliases':
                    await this.showAliasManagementModal(personRecord);
                    break;
                case 'view-details':
                    await this.commands.executeCommand('view-person-detail', { personId: personRecord.id });
                    break;
            }
        } catch (error) {
            this.ui.showDialog('Error', `Failed to perform action: ${error.message}`, 'error');
        }
    }

    async showAliasManagementModal(personRecord) {
        const aliases = await this.data.getPersonAliases(personRecord.id);
        
        const modal = document.createElement('div');
        modal.className = 'modal-overlay';

        const aliasesHTML = aliases.length > 0 
            ? aliases.map(alias => `
                <div class="alias-item" data-alias-id="${alias.id}">
                    <div class="alias-info">
                        <span class="alias-name">${alias.alias_name}</span>
                        <small class="alias-meta">Added ${this.uiUtilities.formatDate(alias.created_at)}</small>
                    </div>
                    <div class="alias-actions">
                        <button class="action-button edit-button" data-action="edit" data-alias-id="${alias.id}" data-alias-name="${alias.alias_name}">Edit</button>
                        <button class="action-button delete-button" data-action="delete" data-alias-id="${alias.id}">Delete</button>
                    </div>
                </div>
            `).join('')
            : '<div class="no-aliases">No aliases found for this person.</div>';

        modal.innerHTML = `
            <div class="modal-dialog large-modal">
                <div class="modal-header">
                    <h3>Manage Aliases - ${personRecord.first_name} ${personRecord.last_name}</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="section-header">
                        <h4>Current Aliases</h4>
                        <button class="primary-button" id="add-new-alias-btn" data-person-id="${personRecord.id}">
                            <span class="button-icon">+</span>
                            Add New Alias
                        </button>
                    </div>
                    <div class="aliases-container">
                        ${aliasesHTML}
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle add new alias
        const addBtn = modal.querySelector('#add-new-alias-btn');
        addBtn.addEventListener('click', async () => {
            await this.addPersonAliasInModal(personRecord.id, modal);
        });

        // Handle edit and delete actions
        modal.addEventListener('click', async (e) => {
            const button = e.target.closest('.action-button');
            if (!button) return;

            const action = button.dataset.action;
            const aliasId = button.dataset.aliasId;

            if (action === 'edit') {
                const aliasName = button.dataset.aliasName;
                await this.editPersonAliasInModal(aliasId, aliasName, personRecord.id, modal);
            } else if (action === 'delete') {
                await this.deletePersonAliasInModal(aliasId, personRecord.id, modal);
            }
        });
    }

    async addPersonAliasInModal(personId, parentModal) {
        const aliasModal = document.createElement('div');
        aliasModal.className = 'modal-overlay';

        aliasModal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Add New Alias</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="new-alias-name">Alias Name:</label>
                        <input type="text" id="new-alias-name" class="form-control" placeholder="Enter alias name..." maxlength="100" required>
                        <small class="form-help">This could be a nickname, street name, or other known name</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="primary-button" id="save-new-alias-btn">Add Alias</button>
                </div>
            </div>
        `;

        document.body.appendChild(aliasModal);

        const aliasInput = aliasModal.querySelector('#new-alias-name');
        const saveBtn = aliasModal.querySelector('#save-new-alias-btn');

        aliasInput.focus();

        const handleSave = async () => {
            const aliasName = aliasInput.value.trim();
            
            if (!aliasName) {
                this.ui.showDialog('Validation Error', 'Please enter an alias name.', 'error');
                return;
            }

            try {
                saveBtn.disabled = true;
                saveBtn.textContent = 'Adding...';

                await this.data.addPersonAlias(personId, aliasName, this.auth.getCurrentUser()?.email);
                
                aliasModal.remove();
                this.ui.showDialog('Success', 'Alias added successfully!', 'success');
                
                // Refresh the parent modal
                parentModal.remove();
                const person = await this.data.get('people', personId);
                await this.showAliasManagementModal(person);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to add alias: ${error.message}`, 'error');
                saveBtn.disabled = false;
                saveBtn.textContent = 'Add Alias';
            }
        };

        saveBtn.addEventListener('click', handleSave);
        aliasInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleSave();
            }
        });
    }

    async editPersonAliasInModal(aliasId, currentName, personId, parentModal) {
        const aliasModal = document.createElement('div');
        aliasModal.className = 'modal-overlay';

        aliasModal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-header">
                    <h3>Edit Alias</h3>
                    <button class="close-button" onclick="this.closest('.modal-overlay').remove()">×</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="edit-alias-name-modal">Alias Name:</label>
                        <input type="text" id="edit-alias-name-modal" class="form-control" value="${currentName}" maxlength="100" required>
                        <small class="form-help">This could be a nickname, street name, or other known name</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="secondary-button" onclick="this.closest('.modal-overlay').remove()">Cancel</button>
                    <button class="primary-button" id="update-alias-modal-btn">Update Alias</button>
                </div>
            </div>
        `;

        document.body.appendChild(aliasModal);

        const aliasInput = aliasModal.querySelector('#edit-alias-name-modal');
        const updateBtn = aliasModal.querySelector('#update-alias-modal-btn');

        aliasInput.focus();
        aliasInput.select();

        const handleUpdate = async () => {
            const aliasName = aliasInput.value.trim();
            
            if (!aliasName) {
                this.ui.showDialog('Validation Error', 'Please enter an alias name.', 'error');
                return;
            }

            if (aliasName === currentName) {
                aliasModal.remove();
                return;
            }

            try {
                updateBtn.disabled = true;
                updateBtn.textContent = 'Updating...';

                await this.data.updatePersonAlias(aliasId, aliasName, this.auth.getCurrentUser()?.email);
                
                aliasModal.remove();
                this.ui.showDialog('Success', 'Alias updated successfully!', 'success');
                
                // Refresh the parent modal
                parentModal.remove();
                const person = await this.data.get('people', personId);
                await this.showAliasManagementModal(person);
            } catch (error) {
                this.ui.showDialog('Error', `Failed to update alias: ${error.message}`, 'error');
                updateBtn.disabled = false;
                updateBtn.textContent = 'Update Alias';
            }
        };

        updateBtn.addEventListener('click', handleUpdate);
        aliasInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleUpdate();
            }
        });
    }

    async deletePersonAliasInModal(aliasId, personId, parentModal) {
        try {
            const aliases = await this.data.getPersonAliases(personId);
            const alias = aliases.find(a => a.id == aliasId);
            
            if (!alias) {
                this.ui.showDialog('Error', 'Alias not found', 'error');
                return;
            }

            this.ui.showConfirmDialog(
                'Delete Alias',
                `Are you sure you want to delete the alias "${alias.alias_name}"? This action cannot be undone.`,
                async () => {
                    try {
                        await this.data.deletePersonAlias(aliasId);
                        this.ui.showDialog('Alias Deleted', `Alias "${alias.alias_name}" has been deleted.`, 'success');
                        
                        // Refresh the parent modal
                        parentModal.remove();
                        const person = await this.data.get('people', personId);
                        await this.showAliasManagementModal(person);
                    } catch (error) {
                        this.ui.showDialog('Error', `Failed to delete alias: ${error.message}`, 'error');
                    }
                }
            );
        } catch (error) {
            console.error('Error deleting alias in modal:', error);
            this.ui.showDialog('Error', 'Failed to delete alias', 'error');
        }
    }

    async checkForUpdatesOnStartup() {
        try {
            // Import UpdateUIManager if not already available
            if (!window.updateUIManager) {
                const { UpdateUIManager } = await import('./updater.js');
                window.updateUIManager = new UpdateUIManager();
            }

            // Check for updates silently (don't show "no update" message)
            await window.updateUIManager.checkForUpdates(false);
        } catch (error) {
            console.log('Update check failed on startup:', error.message);
            // Don't show error to user on startup - updates are optional
        }
    }
} // End of SteviRetroApp class


// Add CSS for content sections and search modals
const additionalCSS = `
/* Weather forecast styles (legacy disabled; compact grid used instead) */
.weather-forecast { margin-top: 0; border-top: none; padding-top: 0; }
.weather-forecast .forecast-header,
.weather-forecast .forecast-list,
.weather-forecast .fh-extra { display: none !important; }

/* Existing content section styles */
.content-section {
    padding: 20px;
}

.content-section h2 {
    color: #ff0000;
    font-size: 20px;
    margin-bottom: 30px;
    border-bottom: 2px solid #ff0000;
    padding-bottom: 10px;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.menu-item {
    border: 2px solid #ff0000;
    padding: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: #000000;
}

.menu-item:hover {
    background: #330000;
    border-color: #ff4444;
}

.menu-icon {
    font-size: 24px;
    margin-bottom: 10px;
    filter: grayscale(100%) sepia(100%) hue-rotate(0deg) brightness(0.8) saturate(2);
}

.menu-title {
    font-size: 16px;
    font-weight: bold;
    color: #ff0000;
    margin-bottom: 5px;
}

.menu-desc {
    font-size: 12px;
    color: #ff4444;
}

/* Search Modal Styles */
.search-form {
    padding: 1rem;
    border: 1px solid #ff0000;
    margin-bottom: 1rem;
    background-color: rgba(255, 0, 0, 0.05);
}

.form-row {
    margin-bottom: 1rem;
}

.form-row label {
    display: block;
    color: #ff0000;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.form-row input,
.form-row select {
    width: 100%;
    padding: 0.5rem;
    background-color: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.form-row input:focus,
.form-row select:focus {
    outline: none;
    border-color: #ff6666;
    background-color: rgba(255, 0, 0, 0.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.form-actions button {
    padding: 0.5rem 1rem;
    background-color: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: 'Courier New', monospace;
    cursor: pointer;
    transition: all 0.2s ease;
}

.form-actions button:hover {
    background-color: rgba(255, 0, 0, 0.1);
    border-color: #ff6666;
}

.search-results {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ff0000;
    padding: 1rem;
    background-color: rgba(0, 0, 0, 0.8);
}

.results-header h4 {
    color: #ff0000;
    font-family: 'Courier New', monospace;
    margin-bottom: 1rem;
    border-bottom: 1px dashed #ff0000;
    padding-bottom: 0.5rem;
}

.results-grid {
    display: grid;
    gap: 1rem;
}

.result-card {
    border: 1px solid #ff0000;
    padding: 1rem;
    background-color: rgba(255, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.2s ease;
}

.result-card:hover {
    background-color: rgba(255, 0, 0, 0.1);
    border-color: #ff6666;
}

.card-header {
    margin-bottom: 0.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px dashed #ff0000;
}

.card-header strong {
    color: #ff0000;
    font-family: 'Courier New', monospace;
}

.org-type {
    float: right;
    font-size: 0.8rem;
    color: #ff6666;
    background-color: rgba(255, 0, 0, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 3px;
}

.card-body {
    margin-bottom: 1rem;
}

.contact-info,
.address-info,
.services-info,
.address-line,
.unit-info,
.notes {
    color: #cccccc;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.metadata {
    color: #888888;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    font-style: italic;
    margin-top: 0.5rem;
}

.card-actions {
    display: flex;
    gap: 0.5rem;
}

.card-actions button {
    padding: 0.3rem 0.8rem;
    background-color: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.card-actions button:hover {
    background-color: rgba(255, 0, 0, 0.1);
    border-color: #ff6666;
}

.no-results {
    text-align: center;
    color: #888888;
    font-family: 'Courier New', monospace;
    padding: 2rem;
}

.detail-section {
    margin-bottom: 2rem;
    border: 1px solid #ff0000;
    padding: 1rem;
    background-color: rgba(255, 0, 0, 0.05);
}

.detail-section h4 {
    color: #ff0000;
    font-family: 'Courier New', monospace;
    margin-bottom: 1rem;
    border-bottom: 1px dashed #ff0000;
    padding-bottom: 0.5rem;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item.full-width {
    grid-column: 1 / -1;
}

.detail-item label {
    color: #ff0000;
    font-family: 'Courier New', monospace;
    font-weight: bold;
    margin-bottom: 0.3rem;
}

.detail-item span {
    color: #cccccc;
    font-family: 'Courier New', monospace;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 0.3rem;
    border: 1px solid #444444;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #ff0000;
}

.modal-actions button {
    padding: 0.5rem 1rem;
    background-color: #000000;
    border: 1px solid #ff0000;
    color: #ff0000;
    font-family: 'Courier New', monospace;
    cursor: pointer;
    transition: all 0.2s ease;
}

.modal-actions button:hover {
    background-color: rgba(255, 0, 0, 0.1);
    border-color: #ff6666;
}
`;

// Inject additional CSS
const style = document.createElement('style');
style.textContent = additionalCSS;
document.head.appendChild(style);

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SteviRetroApp();

    // Set up app shutdown cleanup listener
    if (window.electronAPI && window.electronAPI.ipcRenderer) {
        window.electronAPI.ipcRenderer.on('app-shutdown-cleanup', async () => {
            console.log('🧹 Received shutdown cleanup signal from main process');

            try {
                // Cleanup DataManager and all its resources
                if (window.app && window.app.data) {
                    await window.app.data.cleanup();
                }

                // Cleanup any other app-level resources
                if (window.app) {
                    await window.app.cleanup();
                }

                console.log('✅ Renderer cleanup completed');

                // Notify main process that cleanup is complete
                window.electronAPI.ipcRenderer.invoke('renderer-cleanup-complete');
            } catch (error) {
                console.error('❌ Error during renderer cleanup:', error.message);
                // Still notify main process even if cleanup failed
                window.electronAPI.ipcRenderer.invoke('renderer-cleanup-complete');
            }
        });
    }
});
